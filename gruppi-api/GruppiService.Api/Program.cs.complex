using Microsoft.EntityFrameworkCore;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// CORS configuration
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowFrontend", builder =>
    {
        builder.WithOrigins("http://localhost:3000", "http://localhost:3001")
               .AllowAnyHeader()
               .AllowAnyMethod();
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseCors("AllowFrontend");
app.UseHttpsRedirection();
app.UseAuthorization();

// Simple test endpoint
app.MapGet("/api/gruppi", () => 
{
    return Results.Ok(new[]
    {
        new { gruppiId = 1, unitaproductiveId = 100, codiceGruppo = "GRP001", descrizioneGruppo = "Test Group 1", tipogruppoId = 1 },
        new { gruppiId = 2, unitaproductiveId = 200, codiceGruppo = "GRP002", descrizioneGruppo = "Test Group 2", tipogruppoId = 2 }
    });
});

app.MapControllers();

app.Run();