using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using GruppiService.Application.Services;
using GruppiService.Domain.Entities;

namespace GruppiService.Api.Controllers;

[ApiController]
[Route("api/gruppi")]
[Authorize] // Require authentication for all endpoints
public class GruppiController : ControllerBase
{
    private readonly Application.Services.GruppiService _gruppiService;

    public GruppiController(Application.Services.GruppiService gruppiService)
    {
        _gruppiService = gruppiService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<Gruppo>>> GetAll()
    {
        var gruppi = await _gruppiService.GetAllGruppiAsync();
        return Ok(gruppi);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<Gruppo>> Get(int id)
    {
        var gruppo = await _gruppiService.GetGruppoByIdAsync(id);
        if (gruppo == null)
        {
            return NotFound();
        }
        return Ok(gruppo);
    }

    [HttpPost]
    public async Task<ActionResult<Gruppo>> Create(Gruppo gruppo)
    {
        var createdGruppo = await _gruppiService.CreateGruppoAsync(gruppo);
        return CreatedAtAction(nameof(Get), new { id = createdGruppo.GruppiId }, createdGruppo);
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, Gruppo gruppo)
    {
        if (id != gruppo.GruppiId)
        {
            return BadRequest();
        }

        await _gruppiService.UpdateGruppoAsync(gruppo);
        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        await _gruppiService.DeleteGruppoAsync(id);
        return NoContent();
    }
}