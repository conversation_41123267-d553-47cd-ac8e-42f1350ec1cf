using GruppiService.Domain.Entities;
using GruppiService.Domain.Interfaces;

namespace GruppiService.Application.Services;

public class GruppiService
{
    private readonly IGruppiRepository _gruppiRepository;

    public GruppiService(IGruppiRepository gruppiRepository)
    {
        _gruppiRepository = gruppiRepository;
    }

    public async Task<IEnumerable<Gruppo>> GetAllGruppiAsync()
    {
        return await _gruppiRepository.GetAllAsync();
    }

    public async Task<Gruppo?> GetGruppoByIdAsync(int id)
    {
        return await _gruppiRepository.GetByIdAsync(id);
    }

    public async Task<Gruppo> CreateGruppoAsync(Gruppo gruppo)
    {
        // Generate the next ID manually since the DB table might not have auto-increment
        var existingGruppi = await _gruppiRepository.GetAllAsync();
        var maxId = existingGruppi.Any() ? existingGruppi.Max(g => g.GruppiId) : 0;
        gruppo.GruppiId = maxId + 1;
        
        return await _gruppiRepository.CreateAsync(gruppo);
    }

    public async Task<Gruppo> UpdateGruppoAsync(Gruppo gruppo)
    {
        return await _gruppiRepository.UpdateAsync(gruppo);
    }

    public async Task DeleteGruppoAsync(int id)
    {
        await _gruppiRepository.DeleteAsync(id);
    }
}