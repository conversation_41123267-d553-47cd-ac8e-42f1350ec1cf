using Microsoft.EntityFrameworkCore;
using GruppiService.Domain.Entities;

namespace GruppiService.Infrastructure.Data;

public class GruppiDbContext : DbContext
{
    public GruppiDbContext(DbContextOptions<GruppiDbContext> options) : base(options)
    {
    }

    public DbSet<Gruppo> Gruppi { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.Entity<Gruppo>(entity =>
        {
            entity.ToTable("t_gruppi", "app");
            entity.HasKey(e => e.GruppiId);
            entity.Property(e => e.GruppiId)
                .HasColumnName("gruppi_id");
            entity.Property(e => e.UnitaproductiveId).HasColumnName("unitaproduttive_id");
            entity.Property(e => e.CodiceGruppo).HasColumnName("codice_gruppo").HasMaxLength(50);
            entity.Property(e => e.DescrizioneGruppo).HasColumnName("descrizionegruppo").HasMaxLength(255);
            entity.Property(e => e.TipogruppoId).HasColumnName("tipogruppo_id");
        });
    }
}