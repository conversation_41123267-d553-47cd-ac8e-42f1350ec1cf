using Microsoft.EntityFrameworkCore;
using GruppiService.Domain.Entities;
using GruppiService.Domain.Interfaces;
using GruppiService.Infrastructure.Data;

namespace GruppiService.Infrastructure.Repositories;

public class GruppiRepository : IGruppiRepository
{
    private readonly GruppiDbContext _context;

    public GruppiRepository(GruppiDbContext context)
    {
        _context = context;
    }

    public async Task<IEnumerable<Gruppo>> GetAllAsync()
    {
        return await _context.Gruppi.ToListAsync();
    }

    public async Task<Gruppo?> GetByIdAsync(int id)
    {
        return await _context.Gruppi.FindAsync(id);
    }

    public async Task<Gruppo> CreateAsync(Gruppo gruppo)
    {
        _context.Gruppi.Add(gruppo);
        await _context.SaveChangesAsync();
        return gruppo;
    }

    public async Task<Gruppo> UpdateAsync(Gruppo gruppo)
    {
        _context.Entry(gruppo).State = EntityState.Modified;
        await _context.SaveChangesAsync();
        return gruppo;
    }

    public async Task DeleteAsync(int id)
    {
        var gruppo = await _context.Gruppi.FindAsync(id);
        if (gruppo != null)
        {
            _context.Gruppi.Remove(gruppo);
            await _context.SaveChangesAsync();
        }
    }
}