# Cursor Rules for NDUE Next Generation Project

## 🚫 **CRITICAL: DO NOT CHANGE ARCHITECTURE WITHOUT PERMISSION**

### **Database & Infrastructure Rules:**

- ❌ **NEVER change database providers** (PostgreSQL → InMemory, etc.) without explicit user request
- ❌ **NEVER modify connection strings** or database configuration without permission
- ❌ **NEVER replace existing dependencies** (Npgsql, Entity Framework, etc.) without asking
- ❌ **NEVER change the microservices architecture** without explicit approval

### **When Things Don't Work - DIAGNOSIS ONLY:**

1. **✅ INVESTIGATE** the root cause (missing env vars, services not running, config issues)
2. **✅ SUGGEST** solutions (start services, load env vars, check config)
3. **✅ PROVIDE** commands to diagnose issues
4. **❌ DO NOT** change the codebase as a "quick fix"
5. **❌ DO NOT** replace working components with "simpler" alternatives

### **Dependency Management Rules:**

- ❌ **NEVER add new package references** without explicit user request
- ❌ **NEVER remove existing package references**
- ❌ **NEVER change package versions** without permission
- ✅ **ASK FIRST** before suggesting architectural changes

### **Configuration Rules:**

- ❌ **NEVER hardcode values** that are meant to be configurable
- ❌ **NEVER change environment-specific settings** without permission
- ✅ **RESPECT** existing config patterns (environment variables, appsettings, etc.)
- ✅ **DIAGNOSE** configuration issues rather than bypassing them

### **Authentication & Security Rules:**

- ❌ **NEVER disable or bypass** authentication without explicit request
- ❌ **NEVER change security configurations** without permission
- ❌ **NEVER modify existing auth patterns** (EntraID, JWT, etc.)

### **Error Handling Philosophy:**

```
IF (service_not_working) {
  DIAGNOSE the problem;
  SUGGEST proper solution;
  PROVIDE commands to fix;
  DO NOT change_architecture();
}
```

### **Valid Interventions (Only These):**

- ✅ **Bug fixes** in existing code logic
- ✅ **Adding missing using statements** for existing dependencies
- ✅ **Fixing syntax errors** and compilation issues
- ✅ **Adding configuration** that was missing (but not changing existing)
- ✅ **Security fixes** that don't change architecture

### **Always Ask Permission For:**

- 🤔 Adding new dependencies or packages
- 🤔 Changing database providers or connection patterns
- 🤔 Modifying authentication flows
- 🤔 Changing microservice communication patterns
- 🤔 Replacing existing working solutions
- 🤔 Any architectural modifications

## **Remember: The user has a working system - respect their choices!**

### **Emergency Exceptions:**

Only in case of **critical security vulnerabilities** may these rules be bypassed, and only with immediate explanation to the user.

---

_This rule file ensures we maintain system integrity and respect user decisions about their architecture._
