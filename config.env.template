# Environment Configuration Template
# Copy this template to create environment-specific config files:
# - config.env.dev (Development)
# - config.env.test (Test)
# - config.env.qa (QA)
# - config.env.prod (Production)

# Authentication Configuration (EntraID)
VITE_PORTAL_CLIENT_ID=your-portal-spa-client-id
VITE_TENANT_ID=your-tenant-id
VITE_AUTH_API_CLIENT_ID=your-auth-api-client-id
AZURE_TENANT_ID=your-tenant-id
AZURE_CLIENT_ID=your-auth-api-client-id

# PostgreSQL Database Configuration
# Copy this file to config.env and update with your actual values
PGHOST=your-postgres-host.com
PGUSER=your-username
PGPORT=5432
PGDATABASE=your-database-name
PGPASSWORD=your-password

# Application Ports
PORTAL_PORT=3000
GRUPPI_FRONTEND_PORT=3002
EVENTI_FRONTEND_PORT=3005
GRUPPI_API_PORT=5108
EVENTI_API_PORT=5109
AUTH_API_PORT=8003

# Frontend Application URLs
PORTAL_URL=http://localhost:${PORTAL_PORT}
GRUPPI_FRONTEND_URL=http://localhost:${GRUPPI_FRONTEND_PORT}
EVENTI_FRONTEND_URL=http://localhost:${EVENTI_FRONTEND_PORT}
VITE_PORTAL_URL=http://localhost:${PORTAL_PORT}
VITE_AUTH_API_URL=http://localhost:${AUTH_API_PORT}

# API URLs
GRUPPI_API_URL=http://localhost:${GRUPPI_API_PORT}
EVENTI_API_URL=http://localhost:${EVENTI_API_PORT}
AUTH_API_URL=http://localhost:${AUTH_API_PORT}

# API Documentation URLs
GRUPPI_API_DOCS_URL=http://localhost:${GRUPPI_API_PORT}/swagger
EVENTI_API_DOCS_URL=http://localhost:${EVENTI_API_PORT}/swagger
AUTH_API_DOCS_URL=http://localhost:${AUTH_API_PORT}/swagger

# Environment-specific settings
NODE_ENV=development
ASPNETCORE_ENVIRONMENT=Development
LOG_LEVEL=Debug

# Budget Planning Service Configuration
BUDGET_PLANNING_API_PORT=5111
BUDGET_PLANNING_FRONTEND_PORT=3007
BUDGET_PLANNING_API_URL=http://localhost:${BUDGET_PLANNING_API_PORT}
BUDGET_PLANNING_FRONTEND_URL=http://localhost:${BUDGET_PLANNING_FRONTEND_PORT}
BUDGET_PLANNING_API_DOCS_URL=http://localhost:${BUDGET_PLANNING_API_PORT}/swagger

# Additional environment-specific variables you might need:
# API_BASE_URL=https://api.your-domain.com
# CORS_ORIGINS=https://your-domain.com
# REDIS_CONNECTION_STRING=your-redis-connection
# AZURE_STORAGE_CONNECTION_STRING=your-azure-storage-connection
# budgetplanning Service Configuration
