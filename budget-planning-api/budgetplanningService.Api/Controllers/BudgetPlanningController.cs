using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using BudgetPlanningService.Application.Services;
using BudgetPlanningService.Domain.Entities;

namespace BudgetPlanningService.Api.Controllers;

[ApiController]
[Route("api/budget-planning")]
[Authorize] // Require authentication for all endpoints
public class BudgetPlanningController : ControllerBase
{
    private readonly Application.Services.BudgetPlanningService _budgetPlanningService;

    public BudgetPlanningController(Application.Services.BudgetPlanningService budgetPlanningService)
    {
        _budgetPlanningService = budgetPlanningService;
    }

    // Piano Budget endpoints
    [HttpGet("plans")]
    public async Task<ActionResult<IEnumerable<PianoBudgetAnnuale>>> GetAllPlans()
    {
        var plans = await _budgetPlanningService.GetAllPianiBudgetAsync();
        return Ok(plans);
    }

    [HttpGet("plans/{id}")]
    public async Task<ActionResult<PianoBudgetAnnuale>> GetPlan(int id)
    {
        var plan = await _budgetPlanningService.GetPianoBudgetByIdAsync(id);
        if (plan == null)
        {
            return NotFound();
        }
        return Ok(plan);
    }

    [HttpGet("plans/plant/{impiantoId}")]
    public async Task<ActionResult<IEnumerable<PianoBudgetAnnuale>>> GetPlansByPlant(int impiantoId)
    {
        var plans = await _budgetPlanningService.GetPianiBudgetByImpiantoAsync(impiantoId);
        return Ok(plans);
    }

    [HttpGet("plans/year/{anno}")]
    public async Task<ActionResult<IEnumerable<PianoBudgetAnnuale>>> GetPlansByYear(int anno)
    {
        var plans = await _budgetPlanningService.GetPianiBudgetByAnnoAsync(anno);
        return Ok(plans);
    }

    [HttpPost("plans")]
    public async Task<ActionResult<PianoBudgetAnnuale>> CreatePlan(PianoBudgetAnnuale pianoBudget)
    {
        try
        {
            // Set the user from the authenticated context
            pianoBudget.UtenteCreazione = User.Identity?.Name ?? "System";
            pianoBudget.UtenteUltimaModifica = pianoBudget.UtenteCreazione;

            var createdPlan = await _budgetPlanningService.CreatePianoBudgetAsync(pianoBudget);
            return CreatedAtAction(nameof(GetPlan), new { id = createdPlan.PianoBudgetId }, createdPlan);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return Conflict(ex.Message);
        }
    }

    [HttpPut("plans/{id}")]
    public async Task<IActionResult> UpdatePlan(int id, PianoBudgetAnnuale pianoBudget)
    {
        if (id != pianoBudget.PianoBudgetId)
        {
            return BadRequest("ID mismatch");
        }

        try
        {
            pianoBudget.UtenteUltimaModifica = User.Identity?.Name ?? "System";
            await _budgetPlanningService.UpdatePianoBudgetAsync(pianoBudget);
            return NoContent();
        }
        catch (ArgumentException ex)
        {
            return NotFound(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpDelete("plans/{id}")]
    public async Task<IActionResult> DeletePlan(int id)
    {
        try
        {
            await _budgetPlanningService.DeletePianoBudgetAsync(id);
            return NoContent();
        }
        catch (ArgumentException ex)
        {
            return NotFound(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPost("plans/{impiantoId}/copy")]
    public async Task<ActionResult<PianoBudgetAnnuale>> CopyFromPreviousYear(
        int impiantoId, 
        [FromQuery] int annoOrigine, 
        [FromQuery] int annoDestinazione)
    {
        try
        {
            var copiedPlan = await _budgetPlanningService.CopyPianoBudgetFromPreviousYearAsync(impiantoId, annoOrigine, annoDestinazione);
            if (copiedPlan == null)
            {
                return NotFound($"Piano budget per l'anno {annoOrigine} non trovato per l'impianto {impiantoId}");
            }
            return Ok(copiedPlan);
        }
        catch (InvalidOperationException ex)
        {
            return Conflict(ex.Message);
        }
    }

    [HttpPost("plans/{id}/submit")]
    public async Task<ActionResult<PianoBudgetAnnuale>> SubmitForApproval(int id)
    {
        try
        {
            var utente = User.Identity?.Name ?? "System";
            var updatedPlan = await _budgetPlanningService.SubmitForApprovalAsync(id, utente);
            return Ok(updatedPlan);
        }
        catch (ArgumentException ex)
        {
            return NotFound(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPost("plans/{id}/approve")]
    public async Task<ActionResult<PianoBudgetAnnuale>> ApprovePlan(int id, [FromBody] string? noteApprovazione = null)
    {
        try
        {
            var utenteApprovatore = User.Identity?.Name ?? "System";
            var approvedPlan = await _budgetPlanningService.ApprovePianoBudgetAsync(id, utenteApprovatore, noteApprovazione);
            return Ok(approvedPlan);
        }
        catch (ArgumentException ex)
        {
            return NotFound(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    // Categories endpoints
    [HttpGet("categories")]
    public async Task<ActionResult<IEnumerable<CategoriaBudget>>> GetCategories()
    {
        var categories = await _budgetPlanningService.GetAllCategorieAsync();
        return Ok(categories);
    }

    [HttpGet("categories/active")]
    public async Task<ActionResult<IEnumerable<CategoriaBudget>>> GetActiveCategories()
    {
        var categories = await _budgetPlanningService.GetCategorieAttivaAsync();
        return Ok(categories);
    }

    [HttpGet("categories/type/{tipo}")]
    public async Task<ActionResult<IEnumerable<CategoriaBudget>>> GetCategoriesByType(TipoCategoria tipo)
    {
        var categories = await _budgetPlanningService.GetCategorieByTipoAsync(tipo);
        return Ok(categories);
    }

    // Plants endpoints
    [HttpGet("plants")]
    public async Task<ActionResult<IEnumerable<Impianto>>> GetPlants()
    {
        var plants = await _budgetPlanningService.GetAllImpiantiAsync();
        return Ok(plants);
    }

    [HttpGet("plants/active")]
    public async Task<ActionResult<IEnumerable<Impianto>>> GetActivePlants()
    {
        var plants = await _budgetPlanningService.GetImpiantiAttiviAsync();
        return Ok(plants);
    }

    // Reports endpoints
    [HttpGet("reports/summary/{pianoBudgetId}")]
    public async Task<ActionResult<Dictionary<TipoCategoria, decimal>>> GetBudgetSummary(int pianoBudgetId)
    {
        var summary = await _budgetPlanningService.GetBudgetSummaryByTypeAsync(pianoBudgetId);
        return Ok(summary);
    }

    [HttpGet("reports/monthly/{pianoBudgetId}")]
    public async Task<ActionResult<decimal[]>> GetMonthlyTotals(int pianoBudgetId)
    {
        var monthlyTotals = await _budgetPlanningService.GetMonthlyTotalsAsync(pianoBudgetId);
        return Ok(monthlyTotals);
    }

    [HttpGet("reports/total/{pianoBudgetId}")]
    public async Task<ActionResult<decimal>> GetTotalBudget(int pianoBudgetId)
    {
        var total = await _budgetPlanningService.CalculateTotalBudgetAsync(pianoBudgetId);
        return Ok(total);
    }
}
