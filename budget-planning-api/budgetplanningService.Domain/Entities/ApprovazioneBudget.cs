namespace BudgetPlanningService.Domain.Entities;

/// <summary>
/// Represents a budget approval record
/// </summary>
public class ApprovazioneBudget
{
    public int ApprovazioneId { get; set; }
    public int PianoBudgetId { get; set; }
    public string UtenteApprovatore { get; set; } = string.Empty;
    public DateTime DataApprovazione { get; set; }
    public StatoApprovazione StatoApprovazione { get; set; }
    public string? NoteApprovazione { get; set; }
    
    // Navigation properties
    public virtual PianoBudgetAnnuale? PianoBudget { get; set; }
}

/// <summary>
/// Approval status enumeration
/// </summary>
public enum StatoApprovazione
{
    InAttesa = 0,
    Approvato = 1,
    Rifiutato = 2
}
