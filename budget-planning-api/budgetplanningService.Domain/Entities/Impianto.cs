namespace BudgetPlanningService.Domain.Entities;

/// <summary>
/// Represents a plant/facility for budget planning
/// </summary>
public class Impianto
{
    public int ImpiantoId { get; set; }
    public string CodiceImpianto { get; set; } = string.Empty;
    public string NomeImpianto { get; set; } = string.Empty;
    public string TipoImpianto { get; set; } = string.Empty;
    public string Ubicazione { get; set; } = string.Empty;
    public decimal? CapacitaProduttiva { get; set; }
    public DateTime DataAttivazione { get; set; }
    public StatoImpianto StatoImpianto { get; set; }
    
    // Navigation properties
    public virtual ICollection<PianoBudgetAnnuale> PianiBudget { get; set; } = new List<PianoBudgetAnnuale>();
}

/// <summary>
/// Plant status enumeration
/// </summary>
public enum StatoImpianto
{
    Attivo = 0,
    Inattivo = 1,
    InManutenzione = 2
}
