namespace BudgetPlanningService.Domain.Entities;

/// <summary>
/// Represents an annual budget plan for a plant/facility
/// </summary>
public class PianoBudgetAnnuale
{
    public int PianoBudgetId { get; set; }
    public int ImpiantoId { get; set; }
    public int AnnoBudget { get; set; }
    public StatoBudget StatoBudget { get; set; }
    public DateTime DataCreazione { get; set; }
    public DateTime DataUltimaModifica { get; set; }
    public string UtenteCreazione { get; set; } = string.Empty;
    public string UtenteUltimaModifica { get; set; } = string.Empty;
    public int Versione { get; set; }
    public string? NoteGenerali { get; set; }
    
    // Navigation properties
    public virtual Impianto? Impianto { get; set; }
    public virtual ICollection<VoceBudget> VociBudget { get; set; } = new List<VoceBudget>();
    public virtual ICollection<ApprovazioneBudget> Approvazioni { get; set; } = new List<ApprovazioneBudget>();
}

/// <summary>
/// Budget status enumeration
/// </summary>
public enum StatoBudget
{
    Bozza = 0,
    InRevisione = 1,
    Approvato = 2,
    Archiviato = 3
}
