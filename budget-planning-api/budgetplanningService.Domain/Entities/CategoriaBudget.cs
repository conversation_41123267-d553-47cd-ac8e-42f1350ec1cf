namespace BudgetPlanningService.Domain.Entities;

/// <summary>
/// Represents a budget category (Revenue, Cost, CapEx)
/// </summary>
public class CategoriaBudget
{
    public int CategoriaId { get; set; }
    public string CodiceCategoria { get; set; } = string.Empty;
    public string NomeCategoria { get; set; } = string.Empty;
    public TipoCategoria TipoCategoria { get; set; }
    public int? CategoriaPadreId { get; set; }
    public int OrdineVisualizzazione { get; set; }
    public bool Attiva { get; set; } = true;
    
    // Navigation properties
    public virtual CategoriaBudget? CategoriaPadre { get; set; }
    public virtual ICollection<CategoriaBudget> SottoCategorie { get; set; } = new List<CategoriaBudget>();
    public virtual ICollection<VoceBudget> VociBudget { get; set; } = new List<VoceBudget>();
}

/// <summary>
/// Budget category type enumeration
/// </summary>
public enum TipoCategoria
{
    Ricavi = 0,
    Costi = 1,
    CapEx = 2
}
