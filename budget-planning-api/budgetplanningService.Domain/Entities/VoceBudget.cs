namespace BudgetPlanningService.Domain.Entities;

/// <summary>
/// Represents a budget line item with monthly breakdown
/// </summary>
public class VoceBudget
{
    public int VoceBudgetId { get; set; }
    public int PianoBudgetId { get; set; }
    public int CategoriaId { get; set; }
    public string CodiceVoce { get; set; } = string.Empty;
    public string DescrizioneVoce { get; set; } = string.Empty;
    public decimal ImportoAnnuale { get; set; }
    
    // Monthly breakdown
    public decimal ImportoGen { get; set; }
    public decimal ImportoFeb { get; set; }
    public decimal ImportoMar { get; set; }
    public decimal ImportoApr { get; set; }
    public decimal ImportoMag { get; set; }
    public decimal ImportoGiu { get; set; }
    public decimal ImportoLug { get; set; }
    public decimal ImportoAgo { get; set; }
    public decimal ImportoSet { get; set; }
    public decimal ImportoOtt { get; set; }
    public decimal ImportoNov { get; set; }
    public decimal ImportoDic { get; set; }
    
    public string? NoteVoce { get; set; }
    public DateTime DataCreazione { get; set; }
    public DateTime DataModifica { get; set; }
    
    // Navigation properties
    public virtual PianoBudgetAnnuale? PianoBudget { get; set; }
    public virtual CategoriaBudget? Categoria { get; set; }
    
    /// <summary>
    /// Calculates the total from monthly amounts
    /// </summary>
    public decimal CalcolaTotaleMensile()
    {
        return ImportoGen + ImportoFeb + ImportoMar + ImportoApr + 
               ImportoMag + ImportoGiu + ImportoLug + ImportoAgo + 
               ImportoSet + ImportoOtt + ImportoNov + ImportoDic;
    }
    
    /// <summary>
    /// Validates that annual amount matches monthly total
    /// </summary>
    public bool ValidaCoerenzaImporti()
    {
        var totaleMensile = CalcolaTotaleMensile();
        return Math.Abs(ImportoAnnuale - totaleMensile) < 0.01m;
    }
}
