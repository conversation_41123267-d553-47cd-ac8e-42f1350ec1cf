using BudgetPlanningService.Domain.Entities;

namespace BudgetPlanningService.Domain.Interfaces;

public interface IPianoBudgetRepository
{
    Task<IEnumerable<PianoBudgetAnnuale>> GetAllAsync();
    Task<PianoBudgetAnnuale?> GetByIdAsync(int id);
    Task<IEnumerable<PianoBudgetAnnuale>> GetByImpiantoAsync(int impiantoId);
    Task<IEnumerable<PianoBudgetAnnuale>> GetByAnnoAsync(int anno);
    Task<PianoBudgetAnnuale?> GetByImpiantoAndAnnoAsync(int impiantoId, int anno);
    Task<PianoBudgetAnnuale> CreateAsync(PianoBudgetAnnuale pianoBudget);
    Task<PianoBudgetAnnuale> UpdateAsync(PianoBudgetAnnuale pianoBudget);
    Task DeleteAsync(int id);
    Task<PianoBudgetAnnuale?> CopyFromPreviousYearAsync(int impiantoId, int annoOrigine, int annoDestinazione);
}
