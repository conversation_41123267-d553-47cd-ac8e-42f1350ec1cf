using BudgetPlanningService.Domain.Entities;

namespace BudgetPlanningService.Domain.Interfaces;

public interface IImpiantoRepository
{
    Task<IEnumerable<Impianto>> GetAllAsync();
    Task<Impianto?> GetByIdAsync(int id);
    Task<IEnumerable<Impianto>> GetAttiviAsync();
    Task<Impianto> CreateAsync(Impianto impianto);
    Task<Impianto> UpdateAsync(Impianto impianto);
    Task DeleteAsync(int id);
}
