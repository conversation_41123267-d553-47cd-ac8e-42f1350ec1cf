using BudgetPlanningService.Domain.Entities;

namespace BudgetPlanningService.Domain.Interfaces;

public interface ICategoriaBudgetRepository
{
    Task<IEnumerable<CategoriaBudget>> GetAllAsync();
    Task<CategoriaBudget?> GetByIdAsync(int id);
    Task<IEnumerable<CategoriaBudget>> GetByTipoAsync(TipoCategoria tipo);
    Task<IEnumerable<CategoriaBudget>> GetAttivaAsync();
    Task<CategoriaBudget> CreateAsync(CategoriaBudget categoria);
    Task<CategoriaBudget> UpdateAsync(CategoriaBudget categoria);
    Task DeleteAsync(int id);
}
