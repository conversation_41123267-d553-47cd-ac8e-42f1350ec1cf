using BudgetPlanningService.Domain.Entities;
using BudgetPlanningService.Domain.Interfaces;

namespace BudgetPlanningService.Application.Services;

public class BudgetPlanningService
{
    private readonly IPianoBudgetRepository _pianoBudgetRepository;
    private readonly ICategoriaBudgetRepository _categoriaBudgetRepository;
    private readonly IImpiantoRepository _impiantoRepository;

    public BudgetPlanningService(
        IPianoBudgetRepository pianoBudgetRepository,
        ICategoriaBudgetRepository categoriaBudgetRepository,
        IImpiantoRepository impiantoRepository)
    {
        _pianoBudgetRepository = pianoBudgetRepository;
        _categoriaBudgetRepository = categoriaBudgetRepository;
        _impiantoRepository = impiantoRepository;
    }

    // Piano Budget operations
    public async Task<IEnumerable<PianoBudgetAnnuale>> GetAllPianiBudgetAsync()
    {
        return await _pianoBudgetRepository.GetAllAsync();
    }

    public async Task<PianoBudgetAnnuale?> GetPianoBudgetByIdAsync(int id)
    {
        return await _pianoBudgetRepository.GetByIdAsync(id);
    }

    public async Task<IEnumerable<PianoBudgetAnnuale>> GetPianiBudgetByImpiantoAsync(int impiantoId)
    {
        return await _pianoBudgetRepository.GetByImpiantoAsync(impiantoId);
    }

    public async Task<IEnumerable<PianoBudgetAnnuale>> GetPianiBudgetByAnnoAsync(int anno)
    {
        return await _pianoBudgetRepository.GetByAnnoAsync(anno);
    }

    public async Task<PianoBudgetAnnuale> CreatePianoBudgetAsync(PianoBudgetAnnuale pianoBudget)
    {
        // Validate that the plant exists
        var impianto = await _impiantoRepository.GetByIdAsync(pianoBudget.ImpiantoId);
        if (impianto == null)
            throw new ArgumentException($"Impianto con ID {pianoBudget.ImpiantoId} non trovato");

        // Check if a budget plan already exists for this plant and year
        var esistente = await _pianoBudgetRepository.GetByImpiantoAndAnnoAsync(pianoBudget.ImpiantoId, pianoBudget.AnnoBudget);
        if (esistente != null)
            throw new InvalidOperationException($"Piano budget per l'anno {pianoBudget.AnnoBudget} già esistente per l'impianto {pianoBudget.ImpiantoId}");

        // Validate budget line items
        foreach (var voce in pianoBudget.VociBudget)
        {
            if (!voce.ValidaCoerenzaImporti())
                throw new ArgumentException($"Importo annuale non coerente con il totale mensile per la voce {voce.DescrizioneVoce}");
        }

        return await _pianoBudgetRepository.CreateAsync(pianoBudget);
    }

    public async Task<PianoBudgetAnnuale> UpdatePianoBudgetAsync(PianoBudgetAnnuale pianoBudget)
    {
        var esistente = await _pianoBudgetRepository.GetByIdAsync(pianoBudget.PianoBudgetId);
        if (esistente == null)
            throw new ArgumentException($"Piano budget con ID {pianoBudget.PianoBudgetId} non trovato");

        // Check if budget is approved (cannot be modified)
        if (esistente.StatoBudget == StatoBudget.Approvato)
            throw new InvalidOperationException("Non è possibile modificare un piano budget approvato");

        // Validate budget line items
        foreach (var voce in pianoBudget.VociBudget)
        {
            if (!voce.ValidaCoerenzaImporti())
                throw new ArgumentException($"Importo annuale non coerente con il totale mensile per la voce {voce.DescrizioneVoce}");
        }

        return await _pianoBudgetRepository.UpdateAsync(pianoBudget);
    }

    public async Task DeletePianoBudgetAsync(int id)
    {
        var pianoBudget = await _pianoBudgetRepository.GetByIdAsync(id);
        if (pianoBudget == null)
            throw new ArgumentException($"Piano budget con ID {id} non trovato");

        if (pianoBudget.StatoBudget == StatoBudget.Approvato)
            throw new InvalidOperationException("Non è possibile eliminare un piano budget approvato");

        await _pianoBudgetRepository.DeleteAsync(id);
    }

    public async Task<PianoBudgetAnnuale?> CopyPianoBudgetFromPreviousYearAsync(int impiantoId, int annoOrigine, int annoDestinazione)
    {
        return await _pianoBudgetRepository.CopyFromPreviousYearAsync(impiantoId, annoOrigine, annoDestinazione);
    }

    public async Task<PianoBudgetAnnuale> SubmitForApprovalAsync(int pianoBudgetId, string utente)
    {
        var pianoBudget = await _pianoBudgetRepository.GetByIdAsync(pianoBudgetId);
        if (pianoBudget == null)
            throw new ArgumentException($"Piano budget con ID {pianoBudgetId} non trovato");

        if (pianoBudget.StatoBudget != StatoBudget.Bozza)
            throw new InvalidOperationException("Solo i piani budget in bozza possono essere inviati per approvazione");

        // Validate that the budget has at least one line item
        if (!pianoBudget.VociBudget.Any())
            throw new InvalidOperationException("Il piano budget deve contenere almeno una voce di budget");

        pianoBudget.StatoBudget = StatoBudget.InRevisione;
        pianoBudget.UtenteUltimaModifica = utente;

        return await _pianoBudgetRepository.UpdateAsync(pianoBudget);
    }

    public async Task<PianoBudgetAnnuale> ApprovePianoBudgetAsync(int pianoBudgetId, string utenteApprovatore, string? noteApprovazione = null)
    {
        var pianoBudget = await _pianoBudgetRepository.GetByIdAsync(pianoBudgetId);
        if (pianoBudget == null)
            throw new ArgumentException($"Piano budget con ID {pianoBudgetId} non trovato");

        if (pianoBudget.StatoBudget != StatoBudget.InRevisione)
            throw new InvalidOperationException("Solo i piani budget in revisione possono essere approvati");

        pianoBudget.StatoBudget = StatoBudget.Approvato;
        pianoBudget.UtenteUltimaModifica = utenteApprovatore;

        // Add approval record
        var approvazione = new ApprovazioneBudget
        {
            PianoBudgetId = pianoBudgetId,
            UtenteApprovatore = utenteApprovatore,
            DataApprovazione = DateTime.UtcNow,
            StatoApprovazione = StatoApprovazione.Approvato,
            NoteApprovazione = noteApprovazione
        };
        pianoBudget.Approvazioni.Add(approvazione);

        return await _pianoBudgetRepository.UpdateAsync(pianoBudget);
    }

    // Category operations
    public async Task<IEnumerable<CategoriaBudget>> GetAllCategorieAsync()
    {
        return await _categoriaBudgetRepository.GetAllAsync();
    }

    public async Task<IEnumerable<CategoriaBudget>> GetCategorieAttivaAsync()
    {
        return await _categoriaBudgetRepository.GetAttivaAsync();
    }

    public async Task<IEnumerable<CategoriaBudget>> GetCategorieByTipoAsync(TipoCategoria tipo)
    {
        return await _categoriaBudgetRepository.GetByTipoAsync(tipo);
    }

    // Plant operations
    public async Task<IEnumerable<Impianto>> GetAllImpiantiAsync()
    {
        return await _impiantoRepository.GetAllAsync();
    }

    public async Task<IEnumerable<Impianto>> GetImpiantiAttiviAsync()
    {
        return await _impiantoRepository.GetAttiviAsync();
    }

    // Budget calculations and reports
    public async Task<decimal> CalculateTotalBudgetAsync(int pianoBudgetId)
    {
        var pianoBudget = await _pianoBudgetRepository.GetByIdAsync(pianoBudgetId);
        if (pianoBudget == null)
            return 0;

        return pianoBudget.VociBudget.Sum(v => v.ImportoAnnuale);
    }

    public async Task<Dictionary<TipoCategoria, decimal>> GetBudgetSummaryByTypeAsync(int pianoBudgetId)
    {
        var pianoBudget = await _pianoBudgetRepository.GetByIdAsync(pianoBudgetId);
        if (pianoBudget == null)
            return new Dictionary<TipoCategoria, decimal>();

        return pianoBudget.VociBudget
            .GroupBy(v => v.Categoria!.TipoCategoria)
            .ToDictionary(g => g.Key, g => g.Sum(v => v.ImportoAnnuale));
    }

    public async Task<decimal[]> GetMonthlyTotalsAsync(int pianoBudgetId)
    {
        var pianoBudget = await _pianoBudgetRepository.GetByIdAsync(pianoBudgetId);
        if (pianoBudget == null)
            return new decimal[12];

        var totals = new decimal[12];
        foreach (var voce in pianoBudget.VociBudget)
        {
            totals[0] += voce.ImportoGen;
            totals[1] += voce.ImportoFeb;
            totals[2] += voce.ImportoMar;
            totals[3] += voce.ImportoApr;
            totals[4] += voce.ImportoMag;
            totals[5] += voce.ImportoGiu;
            totals[6] += voce.ImportoLug;
            totals[7] += voce.ImportoAgo;
            totals[8] += voce.ImportoSet;
            totals[9] += voce.ImportoOtt;
            totals[10] += voce.ImportoNov;
            totals[11] += voce.ImportoDic;
        }
        return totals;
    }
}
