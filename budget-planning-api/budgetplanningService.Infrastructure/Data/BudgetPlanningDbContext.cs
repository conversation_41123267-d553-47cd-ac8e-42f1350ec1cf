using Microsoft.EntityFrameworkCore;
using BudgetPlanningService.Domain.Entities;

namespace BudgetPlanningService.Infrastructure.Data;

public class BudgetPlanningDbContext : DbContext
{
    public BudgetPlanningDbContext(DbContextOptions<BudgetPlanningDbContext> options) : base(options)
    {
    }

    public DbSet<PianoBudgetAnnuale> PianiBudgetAnnuali { get; set; }
    public DbSet<CategoriaBudget> CategorieBudget { get; set; }
    public DbSet<VoceBudget> VociBudget { get; set; }
    public DbSet<Impianto> Impianti { get; set; }
    public DbSet<ApprovazioneBudget> ApprovazioniBudget { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure PianoBudgetAnnuale
        modelBuilder.Entity<PianoBudgetAnnuale>(entity =>
        {
            entity.ToTable("t_piano_budget_annuale", "app");
            entity.HasKey(e => e.PianoBudgetId);
            entity.Property(e => e.PianoBudgetId).HasColumnName("piano_budget_id");
            entity.Property(e => e.ImpiantoId).HasColumnName("impianto_id");
            entity.Property(e => e.AnnoBudget).HasColumnName("anno_budget");
            entity.Property(e => e.StatoBudget).HasColumnName("stato_budget").HasConversion<int>();
            entity.Property(e => e.DataCreazione).HasColumnName("data_creazione");
            entity.Property(e => e.DataUltimaModifica).HasColumnName("data_ultima_modifica");
            entity.Property(e => e.UtenteCreazione).HasColumnName("utente_creazione").HasMaxLength(100);
            entity.Property(e => e.UtenteUltimaModifica).HasColumnName("utente_ultima_modifica").HasMaxLength(100);
            entity.Property(e => e.Versione).HasColumnName("versione");
            entity.Property(e => e.NoteGenerali).HasColumnName("note_generali").HasMaxLength(1000);
            
            entity.HasOne(e => e.Impianto)
                  .WithMany(i => i.PianiBudget)
                  .HasForeignKey(e => e.ImpiantoId);
                  
            entity.HasIndex(e => new { e.ImpiantoId, e.AnnoBudget }).IsUnique();
        });

        // Configure CategoriaBudget
        modelBuilder.Entity<CategoriaBudget>(entity =>
        {
            entity.ToTable("t_categorie_budget", "app");
            entity.HasKey(e => e.CategoriaId);
            entity.Property(e => e.CategoriaId).HasColumnName("categoria_id");
            entity.Property(e => e.CodiceCategoria).HasColumnName("codice_categoria").HasMaxLength(20);
            entity.Property(e => e.NomeCategoria).HasColumnName("nome_categoria").HasMaxLength(100);
            entity.Property(e => e.TipoCategoria).HasColumnName("tipo_categoria").HasConversion<int>();
            entity.Property(e => e.CategoriaPadreId).HasColumnName("categoria_padre_id");
            entity.Property(e => e.OrdineVisualizzazione).HasColumnName("ordine_visualizzazione");
            entity.Property(e => e.Attiva).HasColumnName("attiva");
            
            entity.HasOne(e => e.CategoriaPadre)
                  .WithMany(c => c.SottoCategorie)
                  .HasForeignKey(e => e.CategoriaPadreId);
        });

        // Configure VoceBudget
        modelBuilder.Entity<VoceBudget>(entity =>
        {
            entity.ToTable("t_voci_budget", "app");
            entity.HasKey(e => e.VoceBudgetId);
            entity.Property(e => e.VoceBudgetId).HasColumnName("voce_budget_id");
            entity.Property(e => e.PianoBudgetId).HasColumnName("piano_budget_id");
            entity.Property(e => e.CategoriaId).HasColumnName("categoria_id");
            entity.Property(e => e.CodiceVoce).HasColumnName("codice_voce").HasMaxLength(20);
            entity.Property(e => e.DescrizioneVoce).HasColumnName("descrizione_voce").HasMaxLength(200);
            entity.Property(e => e.ImportoAnnuale).HasColumnName("importo_annuale").HasColumnType("decimal(18,2)");
            
            // Monthly amounts
            entity.Property(e => e.ImportoGen).HasColumnName("importo_gen").HasColumnType("decimal(18,2)");
            entity.Property(e => e.ImportoFeb).HasColumnName("importo_feb").HasColumnType("decimal(18,2)");
            entity.Property(e => e.ImportoMar).HasColumnName("importo_mar").HasColumnType("decimal(18,2)");
            entity.Property(e => e.ImportoApr).HasColumnName("importo_apr").HasColumnType("decimal(18,2)");
            entity.Property(e => e.ImportoMag).HasColumnName("importo_mag").HasColumnType("decimal(18,2)");
            entity.Property(e => e.ImportoGiu).HasColumnName("importo_giu").HasColumnType("decimal(18,2)");
            entity.Property(e => e.ImportoLug).HasColumnName("importo_lug").HasColumnType("decimal(18,2)");
            entity.Property(e => e.ImportoAgo).HasColumnName("importo_ago").HasColumnType("decimal(18,2)");
            entity.Property(e => e.ImportoSet).HasColumnName("importo_set").HasColumnType("decimal(18,2)");
            entity.Property(e => e.ImportoOtt).HasColumnName("importo_ott").HasColumnType("decimal(18,2)");
            entity.Property(e => e.ImportoNov).HasColumnName("importo_nov").HasColumnType("decimal(18,2)");
            entity.Property(e => e.ImportoDic).HasColumnName("importo_dic").HasColumnType("decimal(18,2)");
            
            entity.Property(e => e.NoteVoce).HasColumnName("note_voce").HasMaxLength(500);
            entity.Property(e => e.DataCreazione).HasColumnName("data_creazione");
            entity.Property(e => e.DataModifica).HasColumnName("data_modifica");
            
            entity.HasOne(e => e.PianoBudget)
                  .WithMany(p => p.VociBudget)
                  .HasForeignKey(e => e.PianoBudgetId);
                  
            entity.HasOne(e => e.Categoria)
                  .WithMany(c => c.VociBudget)
                  .HasForeignKey(e => e.CategoriaId);
        });

        // Configure Impianto
        modelBuilder.Entity<Impianto>(entity =>
        {
            entity.ToTable("t_impianti", "app");
            entity.HasKey(e => e.ImpiantoId);
            entity.Property(e => e.ImpiantoId).HasColumnName("impianto_id");
            entity.Property(e => e.CodiceImpianto).HasColumnName("codice_impianto").HasMaxLength(20);
            entity.Property(e => e.NomeImpianto).HasColumnName("nome_impianto").HasMaxLength(100);
            entity.Property(e => e.TipoImpianto).HasColumnName("tipo_impianto").HasMaxLength(50);
            entity.Property(e => e.Ubicazione).HasColumnName("ubicazione").HasMaxLength(200);
            entity.Property(e => e.CapacitaProduttiva).HasColumnName("capacita_produttiva").HasColumnType("decimal(18,2)");
            entity.Property(e => e.DataAttivazione).HasColumnName("data_attivazione");
            entity.Property(e => e.StatoImpianto).HasColumnName("stato_impianto").HasConversion<int>();
        });

        // Configure ApprovazioneBudget
        modelBuilder.Entity<ApprovazioneBudget>(entity =>
        {
            entity.ToTable("t_approvazioni_budget", "app");
            entity.HasKey(e => e.ApprovazioneId);
            entity.Property(e => e.ApprovazioneId).HasColumnName("approvazione_id");
            entity.Property(e => e.PianoBudgetId).HasColumnName("piano_budget_id");
            entity.Property(e => e.UtenteApprovatore).HasColumnName("utente_approvatore").HasMaxLength(100);
            entity.Property(e => e.DataApprovazione).HasColumnName("data_approvazione");
            entity.Property(e => e.StatoApprovazione).HasColumnName("stato_approvazione").HasConversion<int>();
            entity.Property(e => e.NoteApprovazione).HasColumnName("note_approvazione").HasMaxLength(500);
            
            entity.HasOne(e => e.PianoBudget)
                  .WithMany(p => p.Approvazioni)
                  .HasForeignKey(e => e.PianoBudgetId);
        });
    }
}
