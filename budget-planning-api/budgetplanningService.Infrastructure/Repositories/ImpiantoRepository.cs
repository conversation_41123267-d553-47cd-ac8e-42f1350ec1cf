using Microsoft.EntityFrameworkCore;
using BudgetPlanningService.Domain.Entities;
using BudgetPlanningService.Domain.Interfaces;
using BudgetPlanningService.Infrastructure.Data;

namespace BudgetPlanningService.Infrastructure.Repositories;

public class ImpiantoRepository : IImpiantoRepository
{
    private readonly BudgetPlanningDbContext _context;

    public ImpiantoRepository(BudgetPlanningDbContext context)
    {
        _context = context;
    }

    public async Task<IEnumerable<Impianto>> GetAllAsync()
    {
        return await _context.Impianti
            .OrderBy(i => i.NomeImpianto)
            .ToListAsync();
    }

    public async Task<Impianto?> GetByIdAsync(int id)
    {
        return await _context.Impianti
            .FirstOrDefaultAsync(i => i.ImpiantoId == id);
    }

    public async Task<IEnumerable<Impianto>> GetAttiviAsync()
    {
        return await _context.Impianti
            .Where(i => i.StatoImpianto == StatoImpianto.Attivo)
            .OrderBy(i => i.NomeImpianto)
            .ToListAsync();
    }

    public async Task<Impianto> CreateAsync(Impianto impianto)
    {
        _context.Impianti.Add(impianto);
        await _context.SaveChangesAsync();
        return impianto;
    }

    public async Task<Impianto> UpdateAsync(Impianto impianto)
    {
        _context.Impianti.Update(impianto);
        await _context.SaveChangesAsync();
        return impianto;
    }

    public async Task DeleteAsync(int id)
    {
        var impianto = await _context.Impianti.FindAsync(id);
        if (impianto != null)
        {
            _context.Impianti.Remove(impianto);
            await _context.SaveChangesAsync();
        }
    }
}
