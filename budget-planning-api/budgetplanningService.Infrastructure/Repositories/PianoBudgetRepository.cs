using Microsoft.EntityFrameworkCore;
using BudgetPlanningService.Domain.Entities;
using BudgetPlanningService.Domain.Interfaces;
using BudgetPlanningService.Infrastructure.Data;

namespace BudgetPlanningService.Infrastructure.Repositories;

public class PianoBudgetRepository : IPianoBudgetRepository
{
    private readonly BudgetPlanningDbContext _context;

    public PianoBudgetRepository(BudgetPlanningDbContext context)
    {
        _context = context;
    }

    public async Task<IEnumerable<PianoBudgetAnnuale>> GetAllAsync()
    {
        return await _context.PianiBudgetAnnuali
            .Include(p => p.Impianto)
            .Include(p => p.VociBudget)
                .ThenInclude(v => v.Categoria)
            .Include(p => p.Approvazioni)
            .ToListAsync();
    }

    public async Task<PianoBudgetAnnuale?> GetByIdAsync(int id)
    {
        return await _context.PianiBudgetAnnuali
            .Include(p => p.Impianto)
            .Include(p => p.VociBudget)
                .ThenInclude(v => v.Categoria)
            .Include(p => p.Approvazioni)
            .FirstOrDefaultAsync(p => p.PianoBudgetId == id);
    }

    public async Task<IEnumerable<PianoBudgetAnnuale>> GetByImpiantoAsync(int impiantoId)
    {
        return await _context.PianiBudgetAnnuali
            .Include(p => p.Impianto)
            .Include(p => p.VociBudget)
                .ThenInclude(v => v.Categoria)
            .Where(p => p.ImpiantoId == impiantoId)
            .OrderByDescending(p => p.AnnoBudget)
            .ToListAsync();
    }

    public async Task<IEnumerable<PianoBudgetAnnuale>> GetByAnnoAsync(int anno)
    {
        return await _context.PianiBudgetAnnuali
            .Include(p => p.Impianto)
            .Include(p => p.VociBudget)
                .ThenInclude(v => v.Categoria)
            .Where(p => p.AnnoBudget == anno)
            .ToListAsync();
    }

    public async Task<PianoBudgetAnnuale?> GetByImpiantoAndAnnoAsync(int impiantoId, int anno)
    {
        return await _context.PianiBudgetAnnuali
            .Include(p => p.Impianto)
            .Include(p => p.VociBudget)
                .ThenInclude(v => v.Categoria)
            .Include(p => p.Approvazioni)
            .FirstOrDefaultAsync(p => p.ImpiantoId == impiantoId && p.AnnoBudget == anno);
    }

    public async Task<PianoBudgetAnnuale> CreateAsync(PianoBudgetAnnuale pianoBudget)
    {
        pianoBudget.DataCreazione = DateTime.UtcNow;
        pianoBudget.DataUltimaModifica = DateTime.UtcNow;
        pianoBudget.Versione = 1;
        
        _context.PianiBudgetAnnuali.Add(pianoBudget);
        await _context.SaveChangesAsync();
        return pianoBudget;
    }

    public async Task<PianoBudgetAnnuale> UpdateAsync(PianoBudgetAnnuale pianoBudget)
    {
        pianoBudget.DataUltimaModifica = DateTime.UtcNow;
        pianoBudget.Versione++;
        
        _context.PianiBudgetAnnuali.Update(pianoBudget);
        await _context.SaveChangesAsync();
        return pianoBudget;
    }

    public async Task DeleteAsync(int id)
    {
        var pianoBudget = await _context.PianiBudgetAnnuali.FindAsync(id);
        if (pianoBudget != null)
        {
            _context.PianiBudgetAnnuali.Remove(pianoBudget);
            await _context.SaveChangesAsync();
        }
    }

    public async Task<PianoBudgetAnnuale?> CopyFromPreviousYearAsync(int impiantoId, int annoOrigine, int annoDestinazione)
    {
        var pianoBudgetOrigine = await GetByImpiantoAndAnnoAsync(impiantoId, annoOrigine);
        if (pianoBudgetOrigine == null)
            return null;

        // Check if destination year already exists
        var esistente = await GetByImpiantoAndAnnoAsync(impiantoId, annoDestinazione);
        if (esistente != null)
            throw new InvalidOperationException($"Piano budget per l'anno {annoDestinazione} già esistente per l'impianto {impiantoId}");

        var nuovoPiano = new PianoBudgetAnnuale
        {
            ImpiantoId = impiantoId,
            AnnoBudget = annoDestinazione,
            StatoBudget = StatoBudget.Bozza,
            UtenteCreazione = pianoBudgetOrigine.UtenteCreazione,
            UtenteUltimaModifica = pianoBudgetOrigine.UtenteCreazione,
            NoteGenerali = $"Copiato dal piano budget {annoOrigine}"
        };

        // Copy budget line items
        foreach (var voceOriginale in pianoBudgetOrigine.VociBudget)
        {
            var nuovaVoce = new VoceBudget
            {
                CategoriaId = voceOriginale.CategoriaId,
                CodiceVoce = voceOriginale.CodiceVoce,
                DescrizioneVoce = voceOriginale.DescrizioneVoce,
                ImportoAnnuale = voceOriginale.ImportoAnnuale,
                ImportoGen = voceOriginale.ImportoGen,
                ImportoFeb = voceOriginale.ImportoFeb,
                ImportoMar = voceOriginale.ImportoMar,
                ImportoApr = voceOriginale.ImportoApr,
                ImportoMag = voceOriginale.ImportoMag,
                ImportoGiu = voceOriginale.ImportoGiu,
                ImportoLug = voceOriginale.ImportoLug,
                ImportoAgo = voceOriginale.ImportoAgo,
                ImportoSet = voceOriginale.ImportoSet,
                ImportoOtt = voceOriginale.ImportoOtt,
                ImportoNov = voceOriginale.ImportoNov,
                ImportoDic = voceOriginale.ImportoDic,
                NoteVoce = voceOriginale.NoteVoce,
                DataCreazione = DateTime.UtcNow,
                DataModifica = DateTime.UtcNow
            };
            nuovoPiano.VociBudget.Add(nuovaVoce);
        }

        return await CreateAsync(nuovoPiano);
    }
}
