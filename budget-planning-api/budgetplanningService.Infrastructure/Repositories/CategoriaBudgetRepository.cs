using Microsoft.EntityFrameworkCore;
using BudgetPlanningService.Domain.Entities;
using BudgetPlanningService.Domain.Interfaces;
using BudgetPlanningService.Infrastructure.Data;

namespace BudgetPlanningService.Infrastructure.Repositories;

public class CategoriaBudgetRepository : ICategoriaBudgetRepository
{
    private readonly BudgetPlanningDbContext _context;

    public CategoriaBudgetRepository(BudgetPlanningDbContext context)
    {
        _context = context;
    }

    public async Task<IEnumerable<CategoriaBudget>> GetAllAsync()
    {
        return await _context.CategorieBudget
            .Include(c => c.CategoriaPadre)
            .Include(c => c.SottoCategorie)
            .OrderBy(c => c.Tipo<PERSON>ategoria)
            .ThenBy(c => c.OrdineVisualizzazione)
            .ToListAsync();
    }

    public async Task<CategoriaBudget?> GetByIdAsync(int id)
    {
        return await _context.CategorieBudget
            .Include(c => c.CategoriaPadre)
            .Include(c => c.SottoCategorie)
            .FirstOrDefaultAsync(c => c.CategoriaId == id);
    }

    public async Task<IEnumerable<CategoriaBudget>> GetByTipoAsync(TipoCategoria tipo)
    {
        return await _context.CategorieBudget
            .Include(c => c.CategoriaPadre)
            .Include(c => c.SottoCategorie)
            .Where(c => c.TipoCategoria == tipo)
            .OrderBy(c => c.OrdineVisualizzazione)
            .ToListAsync();
    }

    public async Task<IEnumerable<CategoriaBudget>> GetAttivaAsync()
    {
        return await _context.CategorieBudget
            .Include(c => c.CategoriaPadre)
            .Include(c => c.SottoCategorie)
            .Where(c => c.Attiva)
            .OrderBy(c => c.TipoCategoria)
            .ThenBy(c => c.OrdineVisualizzazione)
            .ToListAsync();
    }

    public async Task<CategoriaBudget> CreateAsync(CategoriaBudget categoria)
    {
        _context.CategorieBudget.Add(categoria);
        await _context.SaveChangesAsync();
        return categoria;
    }

    public async Task<CategoriaBudget> UpdateAsync(CategoriaBudget categoria)
    {
        _context.CategorieBudget.Update(categoria);
        await _context.SaveChangesAsync();
        return categoria;
    }

    public async Task DeleteAsync(int id)
    {
        var categoria = await _context.CategorieBudget.FindAsync(id);
        if (categoria != null)
        {
            _context.CategorieBudget.Remove(categoria);
            await _context.SaveChangesAsync();
        }
    }
}
