apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ndue-ingress
  namespace: ndue
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://ndue.your-domain.com"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
    nginx.ingress.kubernetes.io/enable-cors: "true"
spec:
  tls:
    - hosts:
        - ndue.your-domain.com
      secretName: ndue-tls
  rules:
    - host: ndue.your-domain.com
      http:
        paths:
          # Portal (main app) - default route
          - path: /()(.*)
            pathType: Prefix
            backend:
              service:
                name: portal-service
                port:
                  number: 80

          # Auth API
          - path: /api/auth(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: auth-api-service
                port:
                  number: 80

          # Gruppi API
          - path: /api/gruppi(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: gruppi-api-service
                port:
                  number: 80

          # Eventi API
          - path: /api/eventi(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: eventi-api-service
                port:
                  number: 80

          # Gruppi Frontend (for standalone access)
          - path: /gruppi(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: gruppi-frontend-service
                port:
                  number: 80

          # Eventi Frontend (for standalone access)
          - path: /eventi(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: eventi-frontend-service
                port:
                  number: 80
