# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# Application specific environment configuration
config.env
config.env.dev
config.env.test
config.env.qa
config.env.prod

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# .NET
## Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Ww][Ii][Nn]32/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/
[Ll]ogs/

## Visual Studio temporary files, build results, and files generated by popular Visual Studio add-ons.
*.tmp
*.temp
*.pdb
*.pgc
*.pgd
*.rsp
*.sbr
*.tlb
*.tli
*.tlh
*.tmp
*.tmp_proj
*_wpftmp.csproj
*.log
*.vspscc
*.vssscc
.builds
*.pidb
*.svclog
*.scc

## Chutzpah Test files
_Chutzpah*

## Visual C++ cache files
ipch/
*.aps
*.ncb
*.opendb
*.opensdf
*.sdf
*.cachefile
*.VC.db
*.VC.VC.opendb

## Visual Studio profiler
*.psess
*.vsp
*.vspx
*.sap

## Visual Studio Trace Files
*.e2e

## TFS 2012 Local Workspace
$tf/

## Guidance Automation Toolkit
*.gpState

## ReSharper is a .NET coding add-in
_ReSharper*/
*.[Rr]e[Ss]harper
*.DotSettings.user

## TeamCity is a build add-in
_TeamCity*

## DotCover is a Code Coverage Tool
*.dotCover

## AxoCover is a Code Coverage Tool
.axoCover/*
!.axoCover/settings.json

## Coverlet is a free, cross platform Code Coverage Tool
coverage*.json
coverage*.xml
coverage*.info

## Visual Studio code coverage results
*.coverage
*.coveragexml

## NCrunch
_NCrunch_*
.*crunch*.local.xml
nCrunchTemp_*

## MightyMoose
*.mm.*
AutoTest.Net/

## Web workbench (sass)
.sass-cache/

## Installshield output folder
[Ee]xpress/

## DocProject is a documentation generator add-in
DocProject/buildhelp/
DocProject/Help/*.HxT
DocProject/Help/*.HxC
DocProject/Help/*.hhc
DocProject/Help/*.hhk
DocProject/Help/*.hhp
DocProject/Help/Html2
DocProject/Help/html

## Click-Once directory
publish/

## Publish Web Output
*.[Pp]ublish.xml
*.azurePubxml
# Note: Comment the next line if you want to checkin your web deploy settings,
# but database connection strings (with potential passwords) will be unencrypted
*.pubxml
*.publishproj

## Microsoft Azure Web App publish settings. Comment the next line if you want to
## checkin your Azure Web App publish settings, but sensitive information contained
## in these files may be unencrypted
PublishScripts/

## NuGet Packages
*.nupkg
# NuGet Symbol Packages
*.snupkg
# The packages folder can be ignored because of Package Restore
**/[Pp]ackages/*
# except build/, which is used as an MSBuild target.
!**/[Pp]ackages/build/
# Uncomment if necessary however generally it will be regenerated when needed
#!**/[Pp]ackages/repositories.config
# NuGet v3's project.json files produces more ignorable files
*.nuget.props
*.nuget.targets

## Microsoft Azure Build Output
csx/
*.build.csdef

## Microsoft Azure Emulator
ecf/
rcf/

## Windows Store app package directories and files
AppPackages/
BundleArtifacts/
Package.StoreAssociation.xml
_pkginfo.txt
*.appx
*.appxbundle
*.appxupload

## Visual Studio cache files
# files ending in .cache can be ignored
*.[Cc]ache
# but keep track of directories ending in .cache
!?*.[Cc]ache/

## Others
ClientBin/
~$*
*~
*.dbmdl
*.dbproj.schemaview
*.jfm
*.pfx
*.publishsettings
orleans.codegen.cs

## Including strong name files can present a security risk
## (https://github.com/github/gitignore/pull/2483#issue-259490424)
#*.snk

## Since there are multiple workflows, uncomment the next line to ignore bower_components
## (https://github.com/github/gitignore/pull/1529#issuecomment-104372622)
#bower_components/

## RIA/Silverlight projects
Generated_Code/

## Backup & report files from converting an old project file
## to a newer Visual Studio version. Backup files are not needed,
## because we have git ;-)
_UpgradeReport_Files/
Backup*/
UpgradeLog*.XML
UpgradeLog*.htm
CConversionReportFiles/

## SQL Server files
*.mdf
*.ldf
*.ndf

## Business Intelligence projects
*.rdl.data
*.bim.layout
*.bim_*.settings
*.rptproj.rsuser
*- [Bb]ackup.rdl
*- [Bb]ackup ([0-9]).rdl
*- [Bb]ackup ([0-9][0-9]).rdl

## Microsoft Fakes
FakesAssemblies/

## GhostDoc plugin setting file
*.GhostDoc.xml

## Node.js Tools for Visual Studio
.ntvs_analysis.dat
node_modules/

## Visual Studio 6 build log
*.plg

## Visual Studio 6 workspace options file
*.opt

## Visual Studio 6 auto-generated workspace file (contains which files were open etc.)
*.vbw

## Visual Studio LightSwitch build output
**/*.HTMLClient/GeneratedArtifacts
**/*.DesktopClient/GeneratedArtifacts
**/*.DesktopClient/ModelManifest.xml
**/*.Server/GeneratedArtiflets
**/*.Server/ModelManifest.xml
_Pvt_Extensions

## Paket dependency manager
.paket/paket.exe
paket-files/

## FAKE - F# Make
.fake/

## CodeRush personal settings
.cr/personal

## Python Tools for Visual Studio (PTVS)
__pycache__/
*.pyc

## Cake - Uncomment if you are using it
# tools/**
# !tools/packages.config

## Tabs Studio
*.tss

## Telerik's JustMock configuration file
*.jmconfig

## BizTalk build output
*.btp.cs
*.btm.cs
*.odx.cs
*.xsd.cs

## OpenCover UI analysis results
OpenCover/

## Azure Stream Analytics local run output
ASALocalRun/

## MSBuild Binary and Structured Log
*.binlog

## NVidia Nsight GPU debugger configuration file
*.nvuser

## MFractors (Xamarin productivity tool) working folder
.mfractor/

## Local History for Visual Studio
.localhistory/

## Visual Studio History (VSHistory) files
.vshistory/

## BeatPulse healthcheck temp database
healthchecksdb

## Backup folder for Package Reference Convert tool in Visual Studio 2017
MigrationBackup/

## Ionide (cross platform F# VS Code tools) working folder
.ionide/

## Fody - auto-generated XML schema
FodyWeavers.xsd

## VS Code files for those working on multiple tools
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# Local History for Visual Studio Code
.history/

## Windows Installer files from build outputs
*.cab
*.msi
*.msix
*.msm
*.msp

## JetBrains Rider
*.sln.iml

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Icon must end with two \r
Icon

# Thumbnails
._*

# Files that might appear in the root of a volume
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Directories potentially created on remote AFP share
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~

# temporary files which can be created if a process still has a handle open of a deleted file
.fuse_hidden*

# KDE directory preferences
.directory

# Linux trash folder which might appear on any partition or disk
.Trash-*

# .nfs files are created when an open file is removed but is still being accessed
.nfs*

# IDE
.idea/
*.swp
*.swo
*~

# Logs
logs
*.log

# Database files
*.db
*.sqlite
*.sqlite3

# Environment files
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
dist/
build/
out/

# Temporary files
tmp/
temp/

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini

# Application specific
# Add any application-specific files that should be ignored 
# Local environment files (not committed)
config.env.local
**/appsettings.Local.json
