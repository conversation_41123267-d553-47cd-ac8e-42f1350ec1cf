# Authentication Implementation Guide

## 📋 Overview

This guide explains how to implement EntraID authentication in the NDUE microservices architecture. The implementation uses a federated approach where the Portal handles authentication and shares tokens with microservice frontends.

## 🏗️ Architecture Components

### Authentication Flow

1. **Portal** authenticates users with EntraID using MSAL.js
2. **Portal** stores tokens in localStorage for sharing
3. **Federated frontends** read tokens and include them in API calls
4. **Business APIs** validate tokens directly with EntraID
5. **Auth API** provides additional token validation services

### Token Types

- **Auth API Token**: Scoped for business API access (`api://36dc2b3a-72c2-4ae7-82e9-61737f8a83e0/access_as_user`)
- **Basic Access Token**: For general user operations (`openid`, `profile`, `email`)

## 🔐 Securing Business APIs

### 1. Add Authentication Packages

Add these NuGet packages to your API projects:

```xml
<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.0" />
<PackageReference Include="Microsoft.Identity.Web" Version="2.15.2" />
```

### 2. Configure Azure AD Settings

Add this configuration to `appsettings.json`:

```json
{
  "AzureAd": {
    "Instance": "https://login.microsoftonline.com/",
    "TenantId": "648900eb-28ff-4b9c-b696-1fdbe561a082",
    "ClientId": "36dc2b3a-72c2-4ae7-82e9-61737f8a83e0",
    "Audience": "api://36dc2b3a-72c2-4ae7-82e9-61737f8a83e0"
  }
}
```

### 3. Configure Authentication in Program.cs

```csharp
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Identity.Web;

var builder = WebApplication.CreateBuilder(args);

// Add Azure AD authentication
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddMicrosoftIdentityWebApi(builder.Configuration.GetSection("AzureAd"));

// Configure CORS for frontend access
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowPortalAndFrontends", policy =>
    {
        policy.WithOrigins(
                "http://localhost:3000",  // Portal
                "http://localhost:3002",  // Gruppi Frontend
                "http://localhost:3005"   // Eventi Frontend
            )
            .AllowAnyMethod()
            .AllowAnyHeader()
            .AllowCredentials();
    });
});

var app = builder.Build();

// Enable authentication and authorization
app.UseAuthentication();
app.UseAuthorization();

// Enable CORS
app.UseCors("AllowPortalAndFrontends");

app.MapControllers();
app.Run();
```

### 4. Secure Controller Endpoints

Add the `[Authorize]` attribute to your controllers:

```csharp
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

[ApiController]
[Route("api/[controller]")]
[Authorize] // Requires authentication for all endpoints
public class GruppiController : ControllerBase
{
    [HttpGet]
    public async Task<ActionResult<IEnumerable<Gruppo>>> GetGruppi()
    {
        // Your existing logic here
        // User identity available via HttpContext.User
    }

    [HttpPost]
    public async Task<ActionResult<Gruppo>> CreateGruppo(Gruppo gruppo)
    {
        // Your existing logic here
    }
}
```

## 🖥️ Frontend Token Sharing Implementation

### 1. Enhanced Portal AuthContext

The Portal manages authentication state and token sharing:

```typescript
// portal/src/auth/AuthContext.tsx
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [isLoginInProgress, setIsLoginInProgress] = useState(false);

  const login = async () => {
    if (isLoginInProgress) return;

    try {
      setIsLoginInProgress(true);

      // Login and get basic token
      const loginResponse = await instance.loginPopup({
        scopes: ["openid", "profile", "email"],
      });

      // Get Auth API token for business APIs
      const authApiTokenResponse = await instance.acquireTokenSilent({
        scopes: ["api://36dc2b3a-72c2-4ae7-82e9-61737f8a83e0/access_as_user"],
        account: loginResponse.account,
      });

      // Store tokens for federated frontends
      localStorage.setItem("ndue_access_token", loginResponse.accessToken);
      localStorage.setItem(
        "ndue_auth_api_token",
        authApiTokenResponse.accessToken
      );
      localStorage.setItem(
        "ndue_user_info",
        JSON.stringify(loginResponse.account)
      );
    } catch (error) {
      console.error("Login failed:", error);
    } finally {
      setIsLoginInProgress(false);
    }
  };

  const logout = async () => {
    try {
      await instance.logoutPopup();

      // Clear stored tokens
      localStorage.removeItem("ndue_access_token");
      localStorage.removeItem("ndue_auth_api_token");
      localStorage.removeItem("ndue_user_info");
    } catch (error) {
      console.error("Logout failed:", error);
    }
  };

  return (
    <AuthContext.Provider value={{ login, logout, isLoginInProgress }}>
      {children}
    </AuthContext.Provider>
  );
};
```

### 2. Automatic Token Headers in API Calls

Enhance the shared `useApiData` hook to automatically include authentication:

```typescript
// shared/ui-components/hooks/useApiData.ts
export const useApiData = <T>(endpoint: string, baseUrl: string) => {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Get token from localStorage (prioritize Auth API token)
        const authApiToken = localStorage.getItem("ndue_auth_api_token");
        const basicToken = localStorage.getItem("ndue_access_token");
        const token = authApiToken || basicToken;

        if (!token) {
          throw new Error("No authentication token available");
        }

        console.log("Using Auth API token for business API call");

        const response = await fetch(`${baseUrl}${endpoint}`, {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          credentials: "include",
        });

        if (response.status === 401) {
          throw new Error("Authentication failed - please login again");
        }

        if (!response.ok) {
          throw new Error(`API call failed: ${response.statusText}`);
        }

        const result = await response.json();
        setData(result);
        setError(null);
      } catch (err) {
        console.error("API call error:", err);
        setError(err instanceof Error ? err.message : "Unknown error");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [endpoint, baseUrl]);

  return { data, loading, error };
};
```

### 3. Prevent MSAL Interaction Conflicts

Implement proper MSAL initialization to prevent conflicts:

```typescript
// portal/src/main.tsx
import { PublicClientApplication } from "@azure/msal-browser";
import { msalConfig } from "./auth/msalConfig";

const msalInstance = new PublicClientApplication(msalConfig);

// Initialize MSAL before rendering
msalInstance.initialize().then(() => {
  // Handle redirect promise
  msalInstance
    .handleRedirectPromise()
    .then((response) => {
      if (response) {
        console.log("Redirect login successful:", response);
      }
    })
    .catch((error) => {
      console.error("Redirect promise error:", error);
    });

  // Add event callbacks
  msalInstance.addEventCallback((event) => {
    if (event.eventType === "msal:loginSuccess") {
      console.log("Login successful via event callback");
    }
  });

  // Render the app
  ReactDOM.createRoot(document.getElementById("root")!).render(
    <React.StrictMode>
      <MsalProvider instance={msalInstance}>
        <App />
      </MsalProvider>
    </React.StrictMode>
  );
});
```

## 🧪 Testing Implementation

### 1. Verify API Security

Test that APIs require authentication:

```bash
# This should return 401 Unauthorized
curl -X GET http://localhost:5108/api/gruppi

# This should work with valid token
curl -X GET http://localhost:5108/api/gruppi \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 2. Test Token Sharing

1. Login to Portal (`http://localhost:3000`)
2. Check browser localStorage for tokens:
   - `ndue_auth_api_token`
   - `ndue_access_token`
   - `ndue_user_info`
3. Navigate to federated frontends and verify they load data

### 3. Debug Authentication Issues

Enable detailed logging in your API:

```csharp
// In Program.cs
builder.Logging.SetMinimumLevel(LogLevel.Debug);
builder.Services.Configure<JwtBearerOptions>(JwtBearerDefaults.AuthenticationScheme, options =>
{
    options.Events = new JwtBearerEvents
    {
        OnAuthenticationFailed = context =>
        {
            Console.WriteLine($"Authentication failed: {context.Exception.Message}");
            return Task.CompletedTask;
        },
        OnTokenValidated = context =>
        {
            Console.WriteLine("Token validated successfully");
            return Task.CompletedTask;
        }
    };
});
```

## 🔧 Advanced Configuration

### Role-Based Authorization

Add role-based access control to specific endpoints:

```csharp
[HttpPost]
[Authorize(Roles = "Admin,Manager")]
public async Task<ActionResult<Gruppo>> CreateGruppo(Gruppo gruppo)
{
    // Only admins and managers can create
}

[HttpDelete("{id}")]
[Authorize(Roles = "Admin")]
public async Task<ActionResult> DeleteGruppo(int id)
{
    // Only admins can delete
}
```

### Claims-Based Authorization

Use custom authorization policies:

```csharp
// In Program.cs
builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("RequireManagerRole", policy =>
        policy.RequireClaim("roles", "Manager"));

    options.AddPolicy("RequireDepartment", policy =>
        policy.RequireClaim("department", "IT", "Finance"));
});

// In controller
[Authorize(Policy = "RequireManagerRole")]
public async Task<ActionResult> ManagerOnlyAction()
{
    // Manager-only functionality
}
```

### Access User Information

Extract user details from validated tokens:

```csharp
[HttpGet("user-info")]
[Authorize]
public ActionResult GetCurrentUser()
{
    var userId = User.FindFirst("sub")?.Value;
    var userName = User.FindFirst("name")?.Value;
    var userEmail = User.FindFirst("email")?.Value;
    var userRoles = User.FindAll("roles").Select(c => c.Value);

    return Ok(new
    {
        Id = userId,
        Name = userName,
        Email = userEmail,
        Roles = userRoles
    });
}
```

## 🛡️ Security Best Practices

### 1. Token Validation

- APIs validate tokens using Azure AD public keys
- No client secrets stored in frontend applications
- Tokens have appropriate scopes for intended use

### 2. CORS Configuration

- Restrict origins to known frontend URLs
- Use `AllowCredentials()` for token sharing
- Avoid wildcard origins in production

### 3. Error Handling

- Don't expose sensitive error details to clients
- Log authentication failures for monitoring
- Provide user-friendly error messages

### 4. Token Management

- Implement automatic token refresh
- Clear tokens on logout
- Handle token expiration gracefully

## 📊 Implementation Checklist

- [ ] **API Security**

  - [ ] Authentication packages installed
  - [ ] Azure AD configuration added
  - [ ] `[Authorize]` attributes applied
  - [ ] CORS properly configured

- [ ] **Frontend Integration**

  - [ ] Portal token acquisition implemented
  - [ ] localStorage token sharing configured
  - [ ] API calls include authentication headers
  - [ ] MSAL interaction conflicts resolved

- [ ] **Testing**

  - [ ] API endpoints reject unauthenticated requests
  - [ ] Authenticated requests work correctly
  - [ ] Token sharing between frontends functional
  - [ ] Error handling works as expected

- [ ] **Production Readiness**
  - [ ] Role-based authorization implemented (if needed)
  - [ ] Proper error logging configured
  - [ ] Security best practices followed
  - [ ] Performance considerations addressed

## 🚀 Deployment Notes

### Environment Variables

Ensure these are set in production:

```bash
AZURE_TENANT_ID=648900eb-28ff-4b9c-b696-1fdbe561a082
AZURE_CLIENT_ID_PORTAL=60bc8a84-b463-4254-84a9-fe360efb2943
AZURE_CLIENT_ID_AUTH_API=36dc2b3a-72c2-4ae7-82e9-61737f8a83e0
```

### SSL/HTTPS Requirements

- EntraID requires HTTPS in production
- Update redirect URIs to use HTTPS
- Configure SSL certificates properly

This implementation provides enterprise-grade authentication with proper separation of concerns and security best practices.
