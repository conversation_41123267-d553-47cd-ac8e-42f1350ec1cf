using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using autho_api.DTOs;
using autho_api.Services;
using System.IdentityModel.Tokens.Jwt;

namespace autho_api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AuthController : ControllerBase
{
    private readonly ITokenValidationService _tokenValidationService;
    private readonly ILogger<AuthController> _logger;

    public AuthController(ITokenValidationService tokenValidationService, ILogger<AuthController> logger)
    {
        _tokenValidationService = tokenValidationService;
        _logger = logger;
    }

    [HttpPost("validate-token")]
    public async Task<ActionResult<TokenValidationResponse>> ValidateToken([FromBody] TokenValidationRequest request)
    {
        if (string.IsNullOrEmpty(request.Token))
        {
            return BadRequest("Token is required");
        }

        var result = await _tokenValidationService.ValidateTokenAsync(request.Token);
        
        if (result.IsValid)
        {
            return Ok(result);
        }

        return Unauthorized(result);
    }

    [HttpGet("user-info")]
    public async Task<ActionResult<UserInfoResponse>> GetUserInfo()
    {
        try
        {
            var authHeader = Request.Headers.Authorization.FirstOrDefault();
            _logger.LogInformation("Received user-info request. Auth header: {AuthHeader}", authHeader);
            
            if (authHeader == null || !authHeader.StartsWith("Bearer "))
            {
                _logger.LogWarning("No valid Bearer token in Authorization header");
                return Unauthorized("No bearer token provided");
            }

            var token = authHeader["Bearer ".Length..];
            _logger.LogInformation("Extracted token: {TokenStart}...", token[..Math.Min(50, token.Length)]);
            
            // Temporarily bypass validation and just decode token
            try
            {
                var handler = new JwtSecurityTokenHandler();
                var jsonToken = handler.ReadJwtToken(token);
                
                _logger.LogInformation("Token decoded successfully. Issuer: {Issuer}, Audience: {Audience}", 
                    jsonToken.Issuer, string.Join(", ", jsonToken.Audiences));
                
                // Handle duplicate claim keys by grouping and taking first value or joining multiple values
                var claimsDict = new Dictionary<string, object>();
                var claimGroups = jsonToken.Claims.GroupBy(c => c.Type);
                
                foreach (var group in claimGroups)
                {
                    if (group.Count() == 1)
                    {
                        claimsDict[group.Key] = group.First().Value;
                    }
                    else
                    {
                        // For multiple values, create an array
                        claimsDict[group.Key] = group.Select(c => c.Value).ToArray();
                    }
                }
                
                var userInfo = new UserInfoResponse
                {
                    Id = GetClaimValue(claimsDict, "oid"),
                    Email = GetClaimValue(claimsDict, "email") ?? GetClaimValue(claimsDict, "preferred_username"),
                    Name = GetClaimValue(claimsDict, "name"),
                    DisplayName = GetClaimValue(claimsDict, "given_name") ?? GetClaimValue(claimsDict, "name"),
                    Roles = new string[0],
                    Claims = claimsDict
                };
                
                _logger.LogInformation("Returning user info for: {Email}", userInfo.Email);
                return Ok(userInfo);
            }
            catch (Exception decodeEx)
            {
                _logger.LogError(decodeEx, "Error decoding token without validation");
                return BadRequest($"Token decode error: {decodeEx.Message}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user info");
            return StatusCode(500, "Internal server error");
        }
    }

    private static string? GetClaimValue(Dictionary<string, object> claims, string claimType)
    {
        if (claims.TryGetValue(claimType, out var value))
        {
            return value switch
            {
                string strValue => strValue,
                string[] arrValue => arrValue.FirstOrDefault(),
                _ => value.ToString()
            };
        }
        return null;
    }

    [HttpPost("debug-token")]
    public IActionResult DebugToken([FromBody] TokenValidationRequest request)
    {
        try
        {
            if (string.IsNullOrEmpty(request.Token))
            {
                return BadRequest("Token is required");
            }

            var handler = new JwtSecurityTokenHandler();
            var jsonToken = handler.ReadJwtToken(request.Token);

            var response = new
            {
                Header = jsonToken.Header,
                Payload = jsonToken.Payload,
                Claims = jsonToken.Claims.Select(c => new { c.Type, c.Value }).ToList(),
                Issuer = jsonToken.Issuer,
                Audiences = jsonToken.Audiences.ToList(),
                ValidFrom = jsonToken.ValidFrom,
                ValidTo = jsonToken.ValidTo,
                IsExpired = jsonToken.ValidTo < DateTime.UtcNow
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error debugging token");
            return BadRequest($"Error parsing token: {ex.Message}");
        }
    }

    [HttpGet("health")]
    public IActionResult Health()
    {
        return Ok(new { Status = "Healthy", Timestamp = DateTime.UtcNow });
    }
}