# NDue Auth API

A stateless authentication service for EntraID (Azure AD) token validation.

## Overview

This service provides JWT token validation for the NDue microservices architecture. It validates tokens issued by EntraID and returns user information for authorized requests.

## Configuration

The service is configured with the following EntraID settings in `appsettings.json`:

- **Tenant ID**: `648900eb-28ff-4b9c-b696-1fdbe561a082`
- **Client ID**: `36dc2b3a-72c2-4ae7-82e9-61737f8a83e0`
- **Audience**: `api://36dc2b3a-72c2-4ae7-82e9-61737f8a83e0`

## Endpoints

### `GET /health`

Health check endpoint

### `POST /api/auth/validate-token`

Validates a JWT token and returns user information

```json
{
  "token": "your_jwt_token_here"
}
```

### `GET /api/auth/user-info`

Returns user information for the provided Bearer token

```
Authorization: Bearer your_jwt_token_here
```

## Running the Service

```bash
dotnet run --urls="http://localhost:8003"
```

## Architecture Notes

- **Stateless**: No database or session storage required
- **Token Validation**: Validates JWT tokens against EntraID
- **CORS**: Configured for local development (ports 3000, 3002, 3005)
- **User Info**: Extracts user claims from validated tokens

## Integration

Other APIs in the microservices architecture can call this service to validate tokens:

```csharp
var response = await httpClient.PostAsJsonAsync(
    "http://localhost:8003/api/auth/validate-token",
    new { token = userToken }
);
```
