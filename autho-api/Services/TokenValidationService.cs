using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Microsoft.IdentityModel.Protocols;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using Microsoft.IdentityModel.Tokens;
using autho_api.DTOs;

namespace autho_api.Services;

public class TokenValidationService : ITokenValidationService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<TokenValidationService> _logger;
    private readonly string _tenantId;
    private readonly string _clientId;
    private readonly string _audience;
    private readonly IConfigurationManager<OpenIdConnectConfiguration> _configurationManager;

    public TokenValidationService(IConfiguration configuration, ILogger<TokenValidationService> logger)
    {
        _configuration = configuration;
        _logger = logger;
        _tenantId = _configuration["AzureAd:TenantId"] ?? throw new InvalidOperationException("TenantId not configured");
        _clientId = _configuration["AzureAd:ClientId"] ?? throw new InvalidOperationException("ClientId not configured");
        _audience = _configuration["AzureAd:Audience"] ?? throw new InvalidOperationException("Audience not configured");
        
        var authority = $"https://login.microsoftonline.com/{_tenantId}/v2.0";
        _configurationManager = new ConfigurationManager<OpenIdConnectConfiguration>(
            $"{authority}/.well-known/openid_configuration",
            new OpenIdConnectConfigurationRetriever(),
            new HttpDocumentRetriever());
    }

    public async Task<TokenValidationResponse> ValidateTokenAsync(string token)
    {
        try
        {
            _logger.LogInformation("Starting token validation...");
            
            var handler = new JwtSecurityTokenHandler();
            
            // First, try to read the token without validation to get basic info
            var jwtToken = handler.ReadJwtToken(token);
            _logger.LogInformation("Token parsed successfully. Issuer: {Issuer}, Audience: {Audience}", 
                jwtToken.Issuer, string.Join(", ", jwtToken.Audiences));
            
            // Get the configuration
            var config = await _configurationManager.GetConfigurationAsync(CancellationToken.None);
            
            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuer = true,
                ValidIssuers = new[]
                {
                    $"https://login.microsoftonline.com/{_tenantId}/v2.0",
                    $"https://sts.windows.net/{_tenantId}/",
                    $"https://login.microsoftonline.com/{_tenantId}/"
                },
                ValidateAudience = true,
                ValidAudiences = new[] { _audience, _clientId },
                ValidateLifetime = true,
                IssuerSigningKeys = config.SigningKeys,
                ValidateIssuerSigningKey = true,
                ClockSkew = TimeSpan.FromMinutes(5)
            };

            _logger.LogInformation("Validation parameters set. Valid audiences: {Audiences}", 
                string.Join(", ", validationParameters.ValidAudiences));

            var principal = handler.ValidateToken(token, validationParameters, out var validatedToken);
            
            _logger.LogInformation("Token validated successfully");
            
            // Extract user information from claims
            var userId = principal.FindFirst(ClaimTypes.NameIdentifier)?.Value 
                        ?? principal.FindFirst("sub")?.Value 
                        ?? principal.FindFirst("oid")?.Value;
            
            var email = principal.FindFirst(ClaimTypes.Email)?.Value 
                       ?? principal.FindFirst("email")?.Value
                       ?? principal.FindFirst("preferred_username")?.Value;
            
            var name = principal.FindFirst(ClaimTypes.Name)?.Value 
                      ?? principal.FindFirst("name")?.Value
                      ?? principal.FindFirst("given_name")?.Value;
            
            var roles = principal.FindAll(ClaimTypes.Role)
                               .Select(c => c.Value)
                               .ToArray();

            return new TokenValidationResponse
            {
                IsValid = true,
                UserId = userId,
                Email = email,
                Name = name,
                Roles = roles
            };
        }
        catch (SecurityTokenValidationException ex)
        {
            _logger.LogWarning(ex, "Token validation failed: {Message}", ex.Message);
            return new TokenValidationResponse
            {
                IsValid = false,
                ErrorMessage = $"Invalid token: {ex.Message}"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating token: {Message}", ex.Message);
            return new TokenValidationResponse
            {
                IsValid = false,
                ErrorMessage = "Token validation error"
            };
        }
    }

    public async Task<UserInfoResponse?> GetUserInfoAsync(string token)
    {
        var validationResult = await ValidateTokenAsync(token);
        
        if (!validationResult.IsValid)
        {
            _logger.LogWarning("Token validation failed for user info request: {Error}", validationResult.ErrorMessage);
            return null;
        }

        try
        {
            var handler = new JwtSecurityTokenHandler();
            var jwtToken = handler.ReadJwtToken(token);
            
            // Extract all claims as a dictionary
            var claims = jwtToken.Claims.ToDictionary(c => c.Type, c => (object)c.Value);
            
            return new UserInfoResponse
            {
                Id = validationResult.UserId ?? string.Empty,
                Email = validationResult.Email ?? string.Empty,
                Name = validationResult.Name ?? string.Empty,
                DisplayName = jwtToken.Claims.FirstOrDefault(c => c.Type == "given_name")?.Value 
                           ?? jwtToken.Claims.FirstOrDefault(c => c.Type == "name")?.Value,
                Roles = validationResult.Roles,
                Claims = claims
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting user info from token");
            return null;
        }
    }
} 