using EventiTeleriscaldamentoService.Domain.Entities;

namespace EventiTeleriscaldamentoService.Domain.Interfaces;

public interface IEventiTeleriscaldamentoRepository
{
    Task<IEnumerable<EventoTeleriscaldamento>> GetAllAsync();
    Task<EventoTeleriscaldamento?> GetByIdAsync(int id);
    Task<EventoTeleriscaldamento> CreateAsync(EventoTeleriscaldamento evento);
    Task<EventoTeleriscaldamento> UpdateAsync(EventoTeleriscaldamento evento);
    Task DeleteAsync(int id);
}