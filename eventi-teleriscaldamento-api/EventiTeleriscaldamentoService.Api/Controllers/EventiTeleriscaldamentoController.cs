using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using EventiTeleriscaldamentoService.Application.Services;
using EventiTeleriscaldamentoService.Domain.Entities;

namespace EventiTeleriscaldamentoService.Api.Controllers;

[ApiController]
[Route("api/eventi-teleriscaldamento")]
[Authorize] // Require authentication for all endpoints
public class EventiTeleriscaldamentoController : ControllerBase
{
    private readonly Application.Services.EventiTeleriscaldamentoService _eventiTeleriscaldamentoService;

    public EventiTeleriscaldamentoController(Application.Services.EventiTeleriscaldamentoService eventiTeleriscaldamentoService)
    {
        _eventiTeleriscaldamentoService = eventiTeleriscaldamentoService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<EventoTeleriscaldamento>>> GetAll()
    {
        var eventi = await _eventiTeleriscaldamentoService.GetAllEventiTeleriscaldamentoAsync();
        return Ok(eventi);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<EventoTeleriscaldamento>> Get(int id)
    {
        var evento = await _eventiTeleriscaldamentoService.GetEventoTeleriscaldamentoByIdAsync(id);
        if (evento == null)
        {
            return NotFound();
        }
        return Ok(evento);
    }

    [HttpPost]
    public async Task<ActionResult<EventoTeleriscaldamento>> Create(EventoTeleriscaldamento evento)
    {
        var createdEvento = await _eventiTeleriscaldamentoService.CreateEventoTeleriscaldamentoAsync(evento);
        return CreatedAtAction(nameof(Get), new { id = createdEvento.SkcEventiteleriscaldamentoId }, createdEvento);
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, EventoTeleriscaldamento evento)
    {
        if (id != evento.SkcEventiteleriscaldamentoId)
        {
            return BadRequest();
        }

        await _eventiTeleriscaldamentoService.UpdateEventoTeleriscaldamentoAsync(evento);
        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        await _eventiTeleriscaldamentoService.DeleteEventoTeleriscaldamentoAsync(id);
        return NoContent();
    }
}