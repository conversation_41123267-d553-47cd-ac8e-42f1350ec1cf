<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>false</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);NU1605</NoWarn>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <EnableNETAnalyzers>false</EnableNETAnalyzers>
    <RunAnalyzersDuringBuild>false</RunAnalyzersDuringBuild>
    <RunCodeAnalysis>false</RunCodeAnalysis>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.11" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.6" />
    <PackageReference Include="Microsoft.Identity.Web" Version="3.9.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.1" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.1" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="../EventiTeleriscaldamentoService.Application/EventiTeleriscaldamentoService.Application.csproj" />
    <ProjectReference Include="../EventiTeleriscaldamentoService.Infrastructure/EventiTeleriscaldamentoService.Infrastructure.csproj" />
  </ItemGroup>

</Project>