using Microsoft.EntityFrameworkCore;
using EventiTeleriscaldamentoService.Domain.Entities;

namespace EventiTeleriscaldamentoService.Infrastructure.Data;

public class EventiTeleriscaldamentoDbContext : DbContext
{
    public EventiTeleriscaldamentoDbContext(DbContextOptions<EventiTeleriscaldamentoDbContext> options) : base(options)
    {
    }

    public DbSet<EventoTeleriscaldamento> EventiTeleriscaldamento { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.Entity<EventoTeleriscaldamento>(entity =>
        {
            entity.ToTable("t_skc_eventiteleriscaldamento", "app");
            entity.HasKey(e => e.SkcEventiteleriscaldamentoId);
            entity.Property(e => e.SkcEventiteleriscaldamentoId).HasColumnName("skc_eventiteleriscaldamento_id");
            entity.Property(e => e.SkConduzioneId).HasColumnName("sk_conduzione_id");
            entity.Property(e => e.DataoraEvento).HasColumnName("dataoraevento");
            entity.Property(e => e.TipoeventoCaldaia).HasColumnName("tipoeventocaldaia").HasMaxLength(100);
            entity.Property(e => e.QuantitativoCalore).HasColumnName("quantitativocalore").HasColumnType("decimal(18,2)");
            entity.Property(e => e.EventocomplementareId).HasColumnName("eventocomplementare_id");
        });
    }
}