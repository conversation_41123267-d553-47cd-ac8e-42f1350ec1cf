using Microsoft.EntityFrameworkCore;
using EventiTeleriscaldamentoService.Domain.Entities;
using EventiTeleriscaldamentoService.Domain.Interfaces;
using EventiTeleriscaldamentoService.Infrastructure.Data;

namespace EventiTeleriscaldamentoService.Infrastructure.Repositories;

public class EventiTeleriscaldamentoRepository : IEventiTeleriscaldamentoRepository
{
    private readonly EventiTeleriscaldamentoDbContext _context;

    public EventiTeleriscaldamentoRepository(EventiTeleriscaldamentoDbContext context)
    {
        _context = context;
    }

    public async Task<IEnumerable<EventoTeleriscaldamento>> GetAllAsync()
    {
        return await _context.EventiTeleriscaldamento.ToListAsync();
    }

    public async Task<EventoTeleriscaldamento?> GetByIdAsync(int id)
    {
        return await _context.EventiTeleriscaldamento.FindAsync(id);
    }

    public async Task<EventoTeleriscaldamento> CreateAsync(EventoTeleriscaldamento evento)
    {
        _context.EventiTeleriscaldamento.Add(evento);
        await _context.SaveChangesAsync();
        return evento;
    }

    public async Task<EventoTeleriscaldamento> UpdateAsync(EventoTeleriscaldamento evento)
    {
        _context.Entry(evento).State = EntityState.Modified;
        await _context.SaveChangesAsync();
        return evento;
    }

    public async Task DeleteAsync(int id)
    {
        var evento = await _context.EventiTeleriscaldamento.FindAsync(id);
        if (evento != null)
        {
            _context.EventiTeleriscaldamento.Remove(evento);
            await _context.SaveChangesAsync();
        }
    }
}