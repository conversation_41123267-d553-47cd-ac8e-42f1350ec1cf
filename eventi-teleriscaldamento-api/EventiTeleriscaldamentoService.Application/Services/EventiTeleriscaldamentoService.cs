using EventiTeleriscaldamentoService.Domain.Entities;
using EventiTeleriscaldamentoService.Domain.Interfaces;

namespace EventiTeleriscaldamentoService.Application.Services;

public class EventiTeleriscaldamentoService
{
    private readonly IEventiTeleriscaldamentoRepository _eventiTeleriscaldamentoRepository;

    public EventiTeleriscaldamentoService(IEventiTeleriscaldamentoRepository eventiTeleriscaldamentoRepository)
    {
        _eventiTeleriscaldamentoRepository = eventiTeleriscaldamentoRepository;
    }

    public async Task<IEnumerable<EventoTeleriscaldamento>> GetAllEventiTeleriscaldamentoAsync()
    {
        return await _eventiTeleriscaldamentoRepository.GetAllAsync();
    }

    public async Task<EventoTeleriscaldamento?> GetEventoTeleriscaldamentoByIdAsync(int id)
    {
        return await _eventiTeleriscaldamentoRepository.GetByIdAsync(id);
    }

    public async Task<EventoTeleriscaldamento> CreateEventoTeleriscaldamentoAsync(EventoTeleriscaldamento evento)
    {
        return await _eventiTeleriscaldamentoRepository.CreateAsync(evento);
    }

    public async Task<EventoTeleriscaldamento> UpdateEventoTeleriscaldamentoAsync(EventoTeleriscaldamento evento)
    {
        return await _eventiTeleriscaldamentoRepository.UpdateAsync(evento);
    }

    public async Task DeleteEventoTeleriscaldamentoAsync(int id)
    {
        await _eventiTeleriscaldamentoRepository.DeleteAsync(id);
    }
}