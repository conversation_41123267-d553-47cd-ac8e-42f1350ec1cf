# Deployment Configuration Guide

## ⚠️ CRITICAL: Authentication Values to Replace for Production

This guide lists all configuration files that contain **development-only** authentication values that **MUST** be replaced with your actual production values before deployment.

### 🔐 Azure AD / EntraID Configuration Values

The following values are currently set to **development/testing values** and must be replaced:

| Configuration Value      | Development Value                      | Action Required                                      |
| ------------------------ | -------------------------------------- | ---------------------------------------------------- |
| **Tenant ID**            | `648900eb-28ff-4b9c-b696-1fdbe561a082` | ⚠️ Replace with your production Azure Tenant ID      |
| **Auth API Client ID**   | `36dc2b3a-72c2-4ae7-82e9-61737f8a83e0` | ⚠️ Replace with your production Auth API Client ID   |
| **Portal SPA Client ID** | `60bc8a84-b463-4254-84a9-fe360efb2943` | ⚠️ Replace with your production Portal SPA Client ID |

---

## 📁 Files Requiring Updates

### 1. **Environment Configuration**

- **File**: `config.env.template`
- **Variables to replace**:
  - `VITE_PORTAL_CLIENT_ID=your-portal-spa-client-id`
  - `VITE_TENANT_ID=your-tenant-id`
  - `VITE_AUTH_API_CLIENT_ID=your-auth-api-client-id`
  - `AZURE_TENANT_ID=your-tenant-id`
  - `AZURE_CLIENT_ID=your-auth-api-client-id`

### 2. **Auth API Configuration**

- **Files**:
  - `autho-api/appsettings.json` (uses environment variables)
  - `autho-api/appsettings.Development.json` (hardcoded dev values)
- **Values to replace in Development.json**:
  - `"TenantId": "648900eb-28ff-4b9c-b696-1fdbe561a082"`
  - `"ClientId": "36dc2b3a-72c2-4ae7-82e9-61737f8a83e0"`
  - `"Audience": "api://36dc2b3a-72c2-4ae7-82e9-61737f8a83e0"`

### 3. **Gruppi API Configuration**

- **Files**:
  - `gruppi-api/GruppiService.Api/appsettings.json`
  - `gruppi-api/GruppiService.Api/appsettings.Development.json`
- **Values to replace**:
  - `"TenantId": "648900eb-28ff-4b9c-b696-1fdbe561a082"`
  - `"ClientId": "36dc2b3a-72c2-4ae7-82e9-61737f8a83e0"`
  - `"Audience": "api://36dc2b3a-72c2-4ae7-82e9-61737f8a83e0"`

### 4. **Eventi Teleriscaldamento API Configuration**

- **Files**:
  - `eventi-teleriscaldamento-api/EventiTeleriscaldamentoService.Api/appsettings.json`
  - `eventi-teleriscaldamento-api/EventiTeleriscaldamentoService.Api/appsettings.Development.json`
- **Values to replace**:
  - `"TenantId": "648900eb-28ff-4b9c-b696-1fdbe561a082"`
  - `"ClientId": "36dc2b3a-72c2-4ae7-82e9-61737f8a83e0"`
  - `"Audience": "api://36dc2b3a-72c2-4ae7-82e9-61737f8a83e0"`

### 5. **Portal Frontend Configuration**

- **File**: `portal/src/auth/msalConfig.ts`
- **Values to replace**:
  - `"60bc8a84-b463-4254-84a9-fe360efb2943"` (Portal SPA Client ID)
  - `"648900eb-28ff-4b9c-b696-1fdbe561a082"` (Tenant ID)
  - `"36dc2b3a-72c2-4ae7-82e9-61737f8a83e0"` (Auth API Client ID)

---

## 🛡️ Security Recommendations

### **Authentication Architecture**

- **Portal SPA**: Uses MSAL.js for browser-based authentication (no secrets needed)
- **Auth API**: Validates tokens using Azure AD public keys (no client secret required)
- **Secure by design**: No client secrets needed for SPA + API validation pattern

### **Environment Separation**

- Use separate Azure AD app registrations for:
  - Development
  - Testing/QA
  - Staging
  - Production
- Each environment should have its own Tenant ID, Client IDs, and secrets

### **Configuration Management**

- Use environment variables in production
- Never hardcode production values in source code
- Consider using Azure App Configuration or similar services
- Implement proper configuration validation at startup

---

## 🚀 Deployment Checklist

- [ ] Create production Azure AD app registrations
- [ ] Update all `appsettings.json` files with production values
- [ ] Set production environment variables
- [ ] Update `config.env` files for each environment
- [ ] Verify CORS origins for production domains
- [ ] Test authentication flow in production environment
- [ ] Verify authentication flow works end-to-end
- [ ] Document production configuration for team

---

## 📞 Support

If you need help with Azure AD app registration setup or configuration, refer to:

- `AUTHENTICATION_SETUP.md` - Detailed setup instructions
- `AUTHENTICATION_IMPLEMENTATION.md` - Implementation details
- Microsoft Azure AD documentation
