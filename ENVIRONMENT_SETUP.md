# Environment Configuration Guide

This document explains how to configure and manage different environments (dev, test, qa, prod) for the NDUE Next Generation Microservices Platform.

## Environment Structure

The project supports multiple environments with separate configuration files:

```
config.env.dev      # Development environment
config.env.test     # Test environment
config.env.qa       # QA environment
config.env.prod     # Production environment
config.env.template # Template for creating new environments
```

## Quick Start

### 1. Choose Your Environment

**Development (Default):**

```bash
./start-dev.sh
# or explicitly
./start-dev.sh dev
```

**Test Environment:**

```bash
./start-dev.sh test
# or use dedicated script
./start-test.sh
```

**QA Environment:**

```bash
./start-dev.sh qa
# or use dedicated script
./start-qa.sh
```

**Production Environment:**

```bash
./start-dev.sh prod
# or use dedicated script
./start-prod.sh
```

### 2. Create Environment Configuration

Copy the template and customize for your environment:

```bash
# For development
cp config.env.template config.env.dev
# Edit config.env.dev with your dev database credentials

# For test
cp config.env.template config.env.test
# Edit config.env.test with your test database credentials

# For QA
cp config.env.template config.env.qa
# Edit config.env.qa with your QA database credentials

# For production
cp config.env.template config.env.prod
# Edit config.env.prod with your production database credentials
```

## Configuration Variables

### Required Variables

All environments must include these PostgreSQL variables:

```bash
PGHOST=your-postgres-host.com
PGUSER=your-username
PGPORT=5432
PGDATABASE=your-database-name
PGPASSWORD=your-password
```

### URL Configuration

The system uses a two-tier approach for URL configuration:

1. **Port Variables** - Define the ports for each service
2. **URL Variables** - Reference the port variables to create complete URLs

This approach provides flexibility and consistency:

```bash
# Define ports
GRUPPI_API_PORT=5108
EVENTI_API_PORT=5109

# URLs automatically use the port variables
GRUPPI_API_URL=http://localhost:${GRUPPI_API_PORT}
EVENTI_API_URL=http://localhost:${EVENTI_API_PORT}
```

**Benefits:**

- Change a port once, all URLs update automatically
- Consistent URL structure across environments
- Easy to switch between localhost and domain names
- Support for different protocols (http/https)

### Optional Variables

Environment-specific settings:

```bash
# Application Ports (usually same across environments)
PORTAL_PORT=3000
GRUPPI_FRONTEND_PORT=3001
EVENTI_FRONTEND_PORT=3005
GRUPPI_API_PORT=5108
EVENTI_API_PORT=5109

# Frontend Application URLs (automatically use port variables)
GRUPPI_FRONTEND_URL=http://localhost:${GRUPPI_FRONTEND_PORT}
EVENTI_FRONTEND_URL=http://localhost:${EVENTI_FRONTEND_PORT}

# API URLs (automatically use port variables)
GRUPPI_API_URL=http://localhost:${GRUPPI_API_PORT}
EVENTI_API_URL=http://localhost:${EVENTI_API_PORT}

# API Documentation URLs (automatically use port variables)
GRUPPI_API_DOCS_URL=http://localhost:${GRUPPI_API_PORT}/swagger
EVENTI_API_DOCS_URL=http://localhost:${EVENTI_API_PORT}/swagger

# Environment Indicators
NODE_ENV=development
ASPNETCORE_ENVIRONMENT=Development
LOG_LEVEL=Debug

# Additional services (uncomment as needed)
# API_BASE_URL=https://api.your-domain.com
# CORS_ORIGINS=https://your-domain.com
# REDIS_CONNECTION_STRING=your-redis-connection
# AZURE_STORAGE_CONNECTION_STRING=your-azure-storage-connection
```

**Note:** URL variables automatically reference port variables using `${PORT_VARIABLE}` syntax. This ensures consistency and makes it easy to change ports without updating URLs manually.

## Environment-Specific Recommendations

### Development (`config.env.dev`)

- Use local or development database
- Enable debug logging
- Use development ports
- Set `NODE_ENV=development`
- Set `ASPNETCORE_ENVIRONMENT=Development`

### Test (`config.env.test`)

- Use dedicated test database
- Use information logging
- Set `NODE_ENV=test`
- Set `ASPNETCORE_ENVIRONMENT=Test`

### QA (`config.env.qa`)

- Use QA database (similar to production)
- Use information logging
- Set `NODE_ENV=qa`
- Set `ASPNETCORE_ENVIRONMENT=QA`

### Production (`config.env.prod`)

- Use production database
- Use warning/error logging only
- Set `NODE_ENV=production`
- Set `ASPNETCORE_ENVIRONMENT=Production`
- Consider using environment variables instead of files

## Security Best Practices

### 1. Never Commit Sensitive Data

- All `config.env.*` files are in `.gitignore`
- Only `config.env.template` is committed
- Use placeholder values in templates

### 2. Production Security

For production environments, consider:

- Using Azure Key Vault or similar service
- Setting environment variables directly in deployment
- Using managed identities for database access

### 3. Database Credentials

- Use different databases for each environment
- Use least-privilege database users
- Rotate passwords regularly
- Use connection pooling in production

## Troubleshooting

### "No configuration file found" Error

```bash
# Create the missing environment file
cp config.env.template config.env.dev
# Edit with your credentials
nano config.env.dev
```

### Database Connection Issues

1. Verify database credentials in your config file
2. Check if database server is accessible
3. Verify network connectivity
4. Check firewall settings

### Port Conflicts

If you get port conflicts:

1. Check what's running on the ports: `lsof -i :3000`
2. Kill conflicting processes: `kill -9 <PID>`
3. Or modify ports in your config file

## CI/CD Integration

### GitHub Actions Example

```yaml
name: Deploy to Environment
on:
  push:
    branches: [main, develop]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2

      - name: Deploy to Test
        if: github.ref == 'refs/heads/develop'
        run: |
          echo "${{ secrets.TEST_DB_PASSWORD }}" > config.env.test
          ./start-test.sh

      - name: Deploy to Production
        if: github.ref == 'refs/heads/main'
        run: |
          echo "${{ secrets.PROD_DB_PASSWORD }}" > config.env.prod
          ./start-prod.sh
```

### Docker Integration

```dockerfile
# Copy template and create environment-specific config
COPY config.env.template /app/config.env.${ENVIRONMENT}
# Set environment variables at runtime
ENV ENVIRONMENT=dev
```

## Migration from Legacy Setup

If you're migrating from the old `config.env` setup:

1. **Backup your current config:**

   ```bash
   cp config.env config.env.backup
   ```

2. **Create environment-specific files:**

   ```bash
   cp config.env config.env.dev
   cp config.env.template config.env.test
   cp config.env.template config.env.qa
   cp config.env.template config.env.prod
   ```

3. **Update each file with appropriate credentials**

4. **Test each environment:**
   ```bash
   ./start-dev.sh dev
   ./start-dev.sh test
   ./start-dev.sh qa
   ./start-dev.sh prod
   ```

## Support

For issues with environment configuration:

1. Check this documentation
2. Verify your config files exist and are properly formatted
3. Ensure all required variables are set
4. Check the startup script output for specific error messages
