# Gruppi Frontend Service

This is the new frontend microservice for the Gruppi service, built with React Router v7 using the Framework Mode approach.

## Features

- React Router v7 with Framework Mode (using `createBrowserRouter`)
- Module Federation for microservice architecture
- TypeScript support
- Shared UI components integration
- API integration with the Gruppi backend service

## Development

```bash
npm install
npm run dev
```

The service will be available at `http://localhost:3002`

## Build

```bash
npm run build
```

## Module Federation

This microservice exposes its `App` component for consumption by the main portal application.
