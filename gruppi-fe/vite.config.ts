import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { federation } from "@module-federation/vite";

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),
    federation({
      name: "gruppiFrontendService",
      filename: "remoteEntry.js",
      exposes: {
        "./App": "./src/App.tsx",
      },
      shared: {
        react: {
          singleton: true,
          requiredVersion: "^18.0.0",
        },
        "react-dom": {
          singleton: true,
          requiredVersion: "^18.0.0",
        },
        "react-router-dom": {
          singleton: true,
          requiredVersion: "^7.0.0",
        },
      },
    }),
  ],
  server: {
    port: 3002,
    cors: true,
  },
  build: {
    target: "esnext",
    minify: false,
    rollupOptions: {
      external: ["react", "react-dom", "react-router-dom"],
    },
  },
  preview: {
    port: 3002,
    cors: true,
  },
});
