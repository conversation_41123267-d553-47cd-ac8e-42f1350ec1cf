import { useState } from "react";
import GruppiList from "./components/GruppiList";
import AddGruppo from "./components/AddGruppo";
import type { Gruppo } from "../../shared/api-contracts/types/gruppo.types";
import "./App.css";

type ViewMode = "list" | "add";

function App() {
  const [currentView, setCurrentView] = useState<ViewMode>("list");

  const handleShowAddForm = () => {
    setCurrentView("add");
  };

  const handleBackToList = () => {
    setCurrentView("list");
  };

  const handleSaveGruppo = (newGruppo: Gruppo) => {
    // Log the new gruppo for debugging
    console.log("New gruppo created successfully:", newGruppo);
    // The AddGruppo component will automatically navigate back to the list
    // The GruppiList component will refresh data when it mounts
  };

  return (
    <div className="app">
      {currentView === "list" && <GruppiList onAddClick={handleShowAddForm} />}
      {currentView === "add" && (
        <AddGruppo onBack={handleBackToList} onSave={handleSaveGruppo} />
      )}
    </div>
  );
}

export default App;
