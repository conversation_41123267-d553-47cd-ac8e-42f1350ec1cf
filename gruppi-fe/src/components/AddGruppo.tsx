import React, { useState } from "react";
import PageContainer from "../../../shared/ui-components/PageContainer/PageContainer";
import LoadingSpinner from "../../../shared/ui-components/LoadingSpinner/LoadingSpinner";
import ErrorMessage from "../../../shared/ui-components/ErrorMessage/ErrorMessage";
import type { Gruppo } from "../../../shared/api-contracts/types/gruppo.types";

interface AddGruppoProps {
  onBack: () => void;
  onSave: (gruppo: Gruppo) => void;
}

const AddGruppo: React.FC<AddGruppoProps> = ({ onBack, onSave }) => {
  const [formData, setFormData] = useState({
    unitaproductiveId: 0,
    codiceGruppo: "",
    descrizioneGruppo: "",
    tipogruppoId: 0,
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const apiUrl = import.meta.env.VITE_API_URL || "http://localhost:5108";

      // Create payload without ID (let the database auto-generate it)
      const payload = {
        unitaproductiveId: formData.unitaproductiveId,
        codiceGruppo: formData.codiceGruppo,
        descrizioneGruppo: formData.descrizioneGruppo,
        tipogruppoId: formData.tipogruppoId,
      };

      const response = await fetch(`${apiUrl}/api/gruppi`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(
          `HTTP error! status: ${response.status}, message: ${errorText}`
        );
      }

      const newGruppo = await response.json();
      setSuccess(true);
      onSave(newGruppo);

      // Auto-navigate back after 2 seconds to show success
      setTimeout(() => {
        onBack();
      }, 2000);
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : "An error occurred while creating the gruppo"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: name.includes("Id") ? parseInt(value) || 0 : value,
    }));
  };

  const isFormValid = () => {
    return (
      formData.unitaproductiveId > 0 &&
      formData.codiceGruppo.trim() !== "" &&
      formData.descrizioneGruppo.trim() !== "" &&
      formData.tipogruppoId > 0
    );
  };

  if (loading) {
    return <LoadingSpinner message="Creating new gruppo..." theme="blue" />;
  }

  if (success) {
    return (
      <PageContainer title="Success!" maxWidth="800px">
        <div className="success-message">
          <h3 style={{ color: "#28a745" }}>✅ Gruppo created successfully!</h3>
          <p>Redirecting back to the list...</p>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer title="Add New Gruppo" maxWidth="800px">
      <div className="add-gruppo-container">
        <div className="form-header">
          <button type="button" onClick={onBack} className="back-button">
            ← Back to Gruppi List
          </button>
        </div>

        {error && <ErrorMessage error={error} />}

        <div className="form-card">
          <h3>Create New Gruppo</h3>
          <form onSubmit={handleSubmit} className="gruppo-form">
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="unitaproductiveId">
                  Unità Produttive ID <span className="required">*</span>
                </label>
                <input
                  type="number"
                  id="unitaproductiveId"
                  name="unitaproductiveId"
                  value={formData.unitaproductiveId || ""}
                  onChange={handleChange}
                  required
                  min="1"
                  className="form-control"
                  placeholder="Enter Unità Produttive ID"
                />
              </div>

              <div className="form-group">
                <label htmlFor="tipogruppoId">
                  Tipo Gruppo ID <span className="required">*</span>
                </label>
                <input
                  type="number"
                  id="tipogruppoId"
                  name="tipogruppoId"
                  value={formData.tipogruppoId || ""}
                  onChange={handleChange}
                  required
                  min="1"
                  className="form-control"
                  placeholder="Enter Tipo Gruppo ID"
                />
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="codiceGruppo">
                Codice Gruppo <span className="required">*</span>
              </label>
              <input
                type="text"
                id="codiceGruppo"
                name="codiceGruppo"
                value={formData.codiceGruppo}
                onChange={handleChange}
                required
                maxLength={50}
                className="form-control"
                placeholder="Enter unique gruppo code (e.g., GRP005)"
              />
            </div>

            <div className="form-group">
              <label htmlFor="descrizioneGruppo">
                Descrizione Gruppo <span className="required">*</span>
              </label>
              <textarea
                id="descrizioneGruppo"
                name="descrizioneGruppo"
                value={formData.descrizioneGruppo}
                onChange={handleChange}
                required
                rows={4}
                maxLength={200}
                className="form-control"
                placeholder="Enter detailed description of the gruppo"
              />
              <div className="character-count">
                {formData.descrizioneGruppo.length}/200 characters
              </div>
            </div>

            <div className="form-actions">
              <button
                type="button"
                onClick={onBack}
                className="btn btn-secondary"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="btn btn-primary"
                disabled={!isFormValid() || loading}
              >
                {loading ? "Creating..." : "Create Gruppo"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </PageContainer>
  );
};

export default AddGruppo;
