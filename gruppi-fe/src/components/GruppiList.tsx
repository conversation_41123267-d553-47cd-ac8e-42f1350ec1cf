import React from "react";
import { useApiData } from "../../../shared/ui-components/hooks/useApiData";
import DataTable from "../../../shared/ui-components/DataTable/DataTable";
import LoadingSpinner from "../../../shared/ui-components/LoadingSpinner/LoadingSpinner";
import ErrorMessage from "../../../shared/ui-components/ErrorMessage/ErrorMessage";
import EmptyState from "../../../shared/ui-components/EmptyState/EmptyState";
import PageContainer from "../../../shared/ui-components/PageContainer/PageContainer";
import type { Gruppo } from "../../../shared/api-contracts/types/gruppo.types";

interface GruppiListProps {
  onAddClick: () => void;
}

const GruppiList: React.FC<GruppiListProps> = ({ onAddClick }) => {
  const {
    data: gruppi,
    loading,
    error,
  } = useApiData<Gruppo>({
    endpoint: "/api/gruppi",
    apiUrl: import.meta.env.VITE_API_URL || "http://localhost:5108",
  });

  const columns = [
    { key: "gruppiId", header: "ID" },
    { key: "unitaproductiveId", header: "Unità Produttive ID" },
    { key: "codiceGruppo", header: "Codice Gruppo" },
    { key: "descrizioneGruppo", header: "Descrizione Gruppo" },
    { key: "tipogruppoId", header: "Tipo Gruppo ID" },
  ];

  if (loading) {
    return <LoadingSpinner message="Loading gruppi..." theme="blue" />;
  }

  if (error) {
    return <ErrorMessage error={error} />;
  }

  return (
    <PageContainer title="Gruppi Management" maxWidth="1200px">
      <div className="page-header">
        <h2>Gruppi List</h2>
        <button onClick={onAddClick} className="add-button">
          + Add New Gruppo
        </button>
      </div>

      {gruppi.length === 0 ? (
        <EmptyState message="No gruppi found" />
      ) : (
        <DataTable columns={columns} data={gruppi} theme="blue" />
      )}
    </PageContainer>
  );
};

export default GruppiList;
