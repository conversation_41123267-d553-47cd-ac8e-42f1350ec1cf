import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { federation } from "@module-federation/vite";

// Template for Vite configuration with Module Federation
// Replace {SERVICE_NAME} with your actual service name
// Replace {PORT} with your service port

export default defineConfig({
  plugins: [
    react(),
    federation({
      name: "{SERVICE_NAME}Fe",
      filename: "remoteEntry.js",
      exposes: {
        "./App": "./src/App.tsx",
        // Add more exposed components as needed
        // "./Component": "./src/components/Component.tsx",
      },
      shared: {
        react: {
          singleton: true,
          requiredVersion: "^18.0.0",
        },
        "react-dom": {
          singleton: true,
          requiredVersion: "^18.0.0",
        },
        "react-router-dom": {
          singleton: true,
          requiredVersion: "^7.0.0",
        },
      },
    }),
  ],
  server: {
    port: {PORT}, // Replace with actual port number
    cors: true,
    host: true, // Allow external connections
  },
  build: {
    target: "esnext",
    rollupOptions: {
      external: ["react", "react-dom"],
    },
  },
  preview: {
    port: {PORT},
    cors: true,
    host: true,
  },
});
