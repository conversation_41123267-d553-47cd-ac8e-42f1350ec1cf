#!/bin/bash

# Script to create a new microservice following the established patterns
# Usage: ./create-new-service.sh <service-name> <api-port> <frontend-port>

set -e

# Make script executable
chmod +x "$0"

# Check if required parameters are provided
if [ $# -ne 3 ]; then
    echo "Usage: $0 <service-name> <api-port> <frontend-port>"
    echo "Example: $0 users 5110 3006"
    exit 1
fi

SERVICE_NAME=$1
API_PORT=$2
FRONTEND_PORT=$3

# Convert service name to PascalC<PERSON> for .NET
SERVICE_NAME_PASCAL=$(echo "$SERVICE_NAME" | sed 's/\b\w/\U&/g' | sed 's/-//g')

echo "🚀 Creating new microservice: $SERVICE_NAME"
echo "   API Port: $API_PORT"
echo "   Frontend Port: $FRONTEND_PORT"
echo "   .NET Service Name: $SERVICE_NAME_PASCAL"

# Create API structure
echo "📦 Creating .NET API structure..."
mkdir -p "${SERVICE_NAME}-api"
cd "${SERVICE_NAME}-api"

# Create solution and projects
dotnet new sln -n "${SERVICE_NAME_PASCAL}Service"
dotnet new webapi -n "${SERVICE_NAME_PASCAL}Service.Api" --framework net9.0
dotnet new classlib -n "${SERVICE_NAME_PASCAL}Service.Application" --framework net9.0
dotnet new classlib -n "${SERVICE_NAME_PASCAL}Service.Domain" --framework net9.0
dotnet new classlib -n "${SERVICE_NAME_PASCAL}Service.Infrastructure" --framework net9.0

# Add projects to solution
dotnet sln add "${SERVICE_NAME_PASCAL}Service.Api"
dotnet sln add "${SERVICE_NAME_PASCAL}Service.Application"
dotnet sln add "${SERVICE_NAME_PASCAL}Service.Domain"
dotnet sln add "${SERVICE_NAME_PASCAL}Service.Infrastructure"

# Add project references
cd "${SERVICE_NAME_PASCAL}Service.Api"
dotnet add reference "../${SERVICE_NAME_PASCAL}Service.Application"
dotnet add reference "../${SERVICE_NAME_PASCAL}Service.Infrastructure"

cd "../${SERVICE_NAME_PASCAL}Service.Application"
dotnet add reference "../${SERVICE_NAME_PASCAL}Service.Domain"

cd "../${SERVICE_NAME_PASCAL}Service.Infrastructure"
dotnet add reference "../${SERVICE_NAME_PASCAL}Service.Domain"
dotnet add reference "../${SERVICE_NAME_PASCAL}Service.Application"

# Add required NuGet packages
cd "../${SERVICE_NAME_PASCAL}Service.Api"
dotnet add package Microsoft.EntityFrameworkCore.Design
dotnet add package Microsoft.AspNetCore.Authentication.JwtBearer
dotnet add package Swashbuckle.AspNetCore

cd "../${SERVICE_NAME_PASCAL}Service.Infrastructure"
dotnet add package Microsoft.EntityFrameworkCore
dotnet add package Npgsql.EntityFrameworkCore.PostgreSQL

cd "../.."

# Create frontend structure
echo "⚛️ Creating React frontend structure..."
mkdir -p "${SERVICE_NAME}-fe"
cd "${SERVICE_NAME}-fe"

# Initialize React project with Vite
npm create vite@latest . -- --template react-ts --yes

# Install additional dependencies
npm install @module-federation/vite

# Create basic component structure
mkdir -p src/components src/hooks src/types

# Create basic App component
cat > src/App.tsx << EOF
import React from 'react';
import './App.css';

function App() {
  return (
    <div className="App">
      <header className="App-header">
        <h1>${SERVICE_NAME_PASCAL} Service</h1>
        <p>Welcome to the ${SERVICE_NAME_PASCAL} micro-frontend!</p>
      </header>
    </div>
  );
}

export default App;
EOF

# Create bootstrap file for Module Federation
cat > src/bootstrap.tsx << EOF
import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import './index.css';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
EOF

# Update main.tsx for Module Federation
cat > src/main.tsx << EOF
import('./bootstrap');
export {};
EOF

cd ".."

# Copy and customize template files
echo "📝 Setting up configuration files..."

# Copy and customize vite.config.ts
if [ -f "templates/vite.config.template.ts" ]; then
    sed "s/{SERVICE_NAME}/${SERVICE_NAME_PASCAL}/g; s/{PORT}/${FRONTEND_PORT}/g" \
        templates/vite.config.template.ts > "${SERVICE_NAME}-fe/vite.config.ts"
fi

# Copy and customize Program.cs
if [ -f "templates/Program.cs.template" ]; then
    sed "s/{ServiceName}/${SERVICE_NAME_PASCAL}/g" \
        templates/Program.cs.template > "${SERVICE_NAME}-api/${SERVICE_NAME_PASCAL}Service.Api/Program.cs"
fi

# Copy and customize package.json
if [ -f "templates/package.json.template" ]; then
    sed "s/{service-name}/${SERVICE_NAME}/g; s/{ServiceName}/${SERVICE_NAME_PASCAL}/g" \
        templates/package.json.template > "${SERVICE_NAME}-fe/package.json"
fi

# Update config.env.template
echo "🔧 Updating configuration template..."
if [ -f "config.env.template" ]; then
    echo "" >> config.env.template
    echo "# ${SERVICE_NAME_PASCAL} Service Configuration" >> config.env.template
    echo "${SERVICE_NAME^^}_API_PORT=${API_PORT}" >> config.env.template
    echo "${SERVICE_NAME^^}_FRONTEND_PORT=${FRONTEND_PORT}" >> config.env.template
    echo "${SERVICE_NAME^^}_API_URL=http://localhost:\${${SERVICE_NAME^^}_API_PORT}" >> config.env.template
    echo "${SERVICE_NAME^^}_FRONTEND_URL=http://localhost:\${${SERVICE_NAME^^}_FRONTEND_PORT}" >> config.env.template
    echo "${SERVICE_NAME^^}_API_DOCS_URL=http://localhost:\${${SERVICE_NAME^^}_API_PORT}/swagger" >> config.env.template
fi

# Create Dockerfile for frontend
cat > "${SERVICE_NAME}-fe/Dockerfile" << EOF
# Use the generic frontend Dockerfile
FROM node:24.3.0-alpine AS build
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production --silent
COPY . .
ARG VITE_PORTAL_CLIENT_ID
ARG VITE_TENANT_ID
ARG VITE_AUTH_API_CLIENT_ID
ARG VITE_PORTAL_URL
ARG VITE_AUTH_API_URL
ENV VITE_PORTAL_CLIENT_ID=\${VITE_PORTAL_CLIENT_ID}
ENV VITE_TENANT_ID=\${VITE_TENANT_ID}
ENV VITE_AUTH_API_CLIENT_ID=\${VITE_AUTH_API_CLIENT_ID}
ENV VITE_PORTAL_URL=\${VITE_PORTAL_URL}
ENV VITE_AUTH_API_URL=\${VITE_AUTH_API_URL}
RUN npm run build

FROM nginx:1.25.3-alpine AS production
COPY --from=build /app/dist /usr/share/nginx/html
COPY ../nginx.conf /etc/nginx/nginx.conf
EXPOSE 8080
CMD ["nginx", "-g", "daemon off;"]
EOF

echo "✅ Microservice '$SERVICE_NAME' created successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Update portal/vite.config.ts to include the new remote:"
echo "   ${SERVICE_NAME}Fe: {"
echo "     type: \"module\","
echo "     name: \"${SERVICE_NAME}Fe\","
echo "     entry: \"http://localhost:${FRONTEND_PORT}/remoteEntry.js\","
echo "   }"
echo ""
echo "2. Add route in portal/src/App.tsx"
echo ""
echo "3. Update start-local.sh to include new services"
echo ""
echo "4. Install frontend dependencies:"
echo "   cd ${SERVICE_NAME}-fe && npm install"
echo ""
echo "5. Test the services:"
echo "   cd ${SERVICE_NAME}-api && dotnet run --project ${SERVICE_NAME_PASCAL}Service.Api"
echo "   cd ${SERVICE_NAME}-fe && npm run dev"
