using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using {ServiceName}.Application.Services;
using {ServiceName}.Domain.Interfaces;
using {ServiceName}.Infrastructure.Data;
using {ServiceName}.Infrastructure.Repositories;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();

// Configure Swagger with authentication
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo 
    { 
        Title = "{ServiceName} API", 
        Version = "v1",
        Description = "API for {ServiceName} service"
    });
    
    // Add JWT authentication to Swagger
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });
    
    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });
});

// Configure Entity Framework
builder.Services.AddDbContext<{ServiceName}DbContext>(options =>
{
    var connectionString = Environment.GetEnvironmentVariable("PGHOST") != null
        ? $"Host={Environment.GetEnvironmentVariable("PGHOST")};" +
          $"Port={Environment.GetEnvironmentVariable("PGPORT") ?? "5432"};" +
          $"Database={Environment.GetEnvironmentVariable("PGDATABASE")};" +
          $"Username={Environment.GetEnvironmentVariable("PGUSER")};" +
          $"Password={Environment.GetEnvironmentVariable("PGPASSWORD")};" +
          "SSL Mode=Prefer;Trust Server Certificate=true;"
        : builder.Configuration.GetConnectionString("DefaultConnection");
    
    options.UseNpgsql(connectionString);
});

// Configure Authentication
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        var tenantId = Environment.GetEnvironmentVariable("AZURE_TENANT_ID") 
                      ?? builder.Configuration["Authentication:AzureAd:TenantId"];
        var clientId = Environment.GetEnvironmentVariable("AZURE_CLIENT_ID") 
                      ?? builder.Configuration["Authentication:AzureAd:ClientId"];
        
        options.Authority = $"https://login.microsoftonline.com/{tenantId}/v2.0";
        options.Audience = clientId;
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ClockSkew = TimeSpan.FromMinutes(5)
        };
    });

builder.Services.AddAuthorization();

// Configure CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// Register application services
builder.Services.AddScoped<I{ServiceName}Repository, {ServiceName}Repository>();
builder.Services.AddScoped<I{ServiceName}Service, {ServiceName}Service>();

// Add health checks
builder.Services.AddHealthChecks()
    .AddDbContextCheck<{ServiceName}DbContext>();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "{ServiceName} API V1");
        c.RoutePrefix = "swagger";
    });
}

app.UseCors("AllowAll");
app.UseAuthentication();
app.UseAuthorization();

// Add health check endpoint
app.MapHealthChecks("/health");

app.MapControllers();

// Ensure database is created
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<{ServiceName}DbContext>();
    try
    {
        context.Database.EnsureCreated();
    }
    catch (Exception ex)
    {
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();
        logger.LogError(ex, "An error occurred while ensuring the database was created.");
    }
}

app.Run();
