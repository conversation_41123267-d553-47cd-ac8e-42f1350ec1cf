import React from "react";
import { useApiData } from "../../../shared/ui-components/hooks/useApiData";
import DataTable from "../../../shared/ui-components/DataTable/DataTable";
import LoadingSpinner from "../../../shared/ui-components/LoadingSpinner/LoadingSpinner";
import ErrorMessage from "../../../shared/ui-components/ErrorMessage/ErrorMessage";
import EmptyState from "../../../shared/ui-components/EmptyState/EmptyState";
import PageContainer from "../../../shared/ui-components/PageContainer/PageContainer";
import type { EventoTeleriscaldamento } from "../../../shared/api-contracts/types/evento.types";
import {
  formatDate,
  formatNumber,
  formatNullable,
} from "../../../shared/utilities/formatters";

const EventiTeleriscaldamentoList: React.FC = () => {
  const {
    data: eventi,
    loading,
    error,
  } = useApiData<EventoTeleriscaldamento>({
    endpoint: "/api/eventi-teleriscaldamento",
    apiUrl: import.meta.env.VITE_API_URL || "http://localhost:5109",
  });

  const columns = [
    { key: "skcEventiteleriscaldamentoId", header: "ID Evento" },
    { key: "skConduzioneId", header: "ID Conduzione" },
    {
      key: "dataoraEvento",
      header: "Data/Ora Evento",
      render: (value: string) => formatDate(value),
    },
    { key: "tipoeventoCaldaia", header: "Tipo Evento Caldaia" },
    {
      key: "quantitativoCalore",
      header: "Quantitativo Calore",
      render: (value: number) => formatNumber(value),
    },
    {
      key: "eventocomplementareId",
      header: "ID Evento Complementare",
      render: (value: number | null) => formatNullable(value),
    },
  ];

  if (loading) {
    return (
      <LoadingSpinner
        message="Loading eventi teleriscaldamento..."
        theme="green"
      />
    );
  }

  if (error) {
    return <ErrorMessage error={error} />;
  }

  return (
    <PageContainer
      title="Eventi Teleriscaldamento Management"
      maxWidth="1400px"
    >
      {eventi.length === 0 ? (
        <EmptyState message="No eventi teleriscaldamento found" />
      ) : (
        <DataTable columns={columns} data={eventi} theme="green" />
      )}
    </PageContainer>
  );
};

export default EventiTeleriscaldamentoList;
