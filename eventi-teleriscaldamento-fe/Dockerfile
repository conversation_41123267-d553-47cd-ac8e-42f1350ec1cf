# Dockerfile for Eventi Teleriscaldamento Micro-Frontend
# NDUE Next Generation Microservices Platform

#############################
# Stage 1: Build Environment
#############################
FROM node:24.3.0-alpine AS build

# Set working directory
WORKDIR /app

# Install build dependencies
RUN apk add --no-cache python3 make g++ git

# Copy package files for better Docker layer caching
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production --silent

# Copy source code
COPY . .

# Build arguments for Eventi-specific environment variables
ARG VITE_EVENTI_API_URL
ARG VITE_DOMAIN
ARG NODE_ENV=production

# Set environment variables for build
ENV VITE_EVENTI_API_URL=${VITE_EVENTI_API_URL}
ENV VITE_DOMAIN=${VITE_DOMAIN}
ENV NODE_ENV=${NODE_ENV}

# Build the Eventi micro-frontend
RUN npm run build

# Verify Module Federation remoteEntry.js is created
RUN ls -la dist/ && \
    ls -la dist/remoteEntry.js && \
    echo "Eventi micro-frontend build completed successfully" && \
    du -sh dist/

#############################
# Stage 2: Production Server
#############################
FROM nginx:1.25.3-alpine AS production

# Install curl for health checks
RUN apk add --no-cache curl

# Remove default nginx content
RUN rm -rf /usr/share/nginx/html/*

# Copy built Eventi application
COPY --from=build /app/dist /usr/share/nginx/html

# Copy micro-frontend nginx configuration
COPY ../nginx.conf /etc/nginx/nginx.conf

# Set proper permissions
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chmod -R 755 /usr/share/nginx/html && \
    chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d && \
    mkdir -p /var/run/nginx && \
    chown nginx:nginx /var/run/nginx

# Switch to non-root user
USER nginx

# Expose port 8080
EXPOSE 8080

# Health check for Eventi frontend
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/remoteEntry.js || exit 1

# Labels for container management
LABEL org.opencontainers.image.title="NDUE Eventi Frontend"
LABEL org.opencontainers.image.description="Eventi Teleriscaldamento micro-frontend with Module Federation"
LABEL org.opencontainers.image.vendor="NDUE"
LABEL org.opencontainers.image.version="1.0.0"
LABEL service.type="micro-frontend"
LABEL service.module-federation.name="eventiTeleriscaldamentoFe"

# Start nginx
CMD ["nginx", "-g", "daemon off;"] 