# Generic Dockerfile for React + Vite Frontend Applications
# Optimized for NDUE microservices with Module Federation

#############################
# Stage 1: Build Environment
#############################
FROM node:24.3.0-alpine AS build

# Set working directory
WORKDIR /app

# Install build dependencies for native modules
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git

# Copy package files
COPY package*.json ./

# Install dependencies with npm ci for production builds
RUN npm ci --only=production --silent

# Copy source code
COPY . .

# Build arguments for environment variables
ARG VITE_PORTAL_CLIENT_ID
ARG VITE_TENANT_ID
ARG VITE_AUTH_API_CLIENT_ID
ARG VITE_PORTAL_URL
ARG VITE_AUTH_API_URL
ARG VITE_DOMAIN
ARG NODE_ENV=production

# Set environment variables for build
ENV VITE_PORTAL_CLIENT_ID=${VITE_PORTAL_CLIENT_ID}
ENV VITE_TENANT_ID=${VITE_TENANT_ID}
ENV VITE_AUTH_API_CLIENT_ID=${VITE_AUTH_API_CLIENT_ID}
ENV VITE_PORTAL_URL=${VITE_PORTAL_URL}
ENV VITE_AUTH_API_URL=${VITE_AUTH_API_URL}
ENV VITE_DOMAIN=${VITE_DOMAIN}
ENV NODE_ENV=${NODE_ENV}

# Build the application
RUN npm run build

# Verify build output
RUN ls -la dist/ && \
    echo "Build completed successfully" && \
    du -sh dist/

#############################
# Stage 2: Production Server
#############################
FROM nginx:1.25.3-alpine AS production

# Install curl for health checks
RUN apk add --no-cache curl

# Remove default nginx static assets
RUN rm -rf /usr/share/nginx/html/*

# Copy built application from build stage
COPY --from=build /app/dist /usr/share/nginx/html

# Copy custom nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Create nginx user and set permissions
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chmod -R 755 /usr/share/nginx/html && \
    chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d

# Create directory for PID file
RUN mkdir -p /var/run/nginx && \
    chown nginx:nginx /var/run/nginx

# Switch to non-root user
USER nginx

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/ || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"] 