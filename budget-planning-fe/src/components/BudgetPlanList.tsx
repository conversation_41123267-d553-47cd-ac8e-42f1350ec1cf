import React, { useState, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { useBudgetPlans, useBudgetPlants } from '../hooks/useBudgetApi';
import { 
  PianoBudgetAnnuale, 
  StatoBudget, 
  STATO_BUDGET_LABELS,
  BudgetPlanListFilters 
} from '../types/budget.types';
import LoadingSpinner from './LoadingSpinner';
import ErrorMessage from './ErrorMessage';

const BudgetPlanList: React.FC = () => {
  const { plans, loading, error, refetch } = useBudgetPlans();
  const { plants } = useBudgetPlants();
  const [filters, setFilters] = useState<BudgetPlanListFilters>({});

  const filteredPlans = useMemo(() => {
    return plans.filter(plan => {
      if (filters.impiantoId && plan.impiantoId !== filters.impiantoId) return false;
      if (filters.annoBudget && plan.annoBudget !== filters.annoBudget) return false;
      if (filters.statoBudget !== undefined && plan.statoBudget !== filters.statoBudget) return false;
      if (filters.searchText) {
        const searchLower = filters.searchText.toLowerCase();
        const matchesSearch = 
          plan.impianto?.nomeImpianto.toLowerCase().includes(searchLower) ||
          plan.impianto?.codiceImpianto.toLowerCase().includes(searchLower) ||
          plan.noteGenerali?.toLowerCase().includes(searchLower);
        if (!matchesSearch) return false;
      }
      return true;
    });
  }, [plans, filters]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('it-IT', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const calculateTotalBudget = (plan: PianoBudgetAnnuale) => {
    return plan.vociBudget.reduce((total, voce) => total + voce.importoAnnuale, 0);
  };

  const getStatusBadgeClass = (status: StatoBudget) => {
    switch (status) {
      case StatoBudget.Bozza: return 'badge-draft';
      case StatoBudget.InRevisione: return 'badge-review';
      case StatoBudget.Approvato: return 'badge-approved';
      case StatoBudget.Archiviato: return 'badge-archived';
      default: return 'badge-default';
    }
  };

  if (loading) return <LoadingSpinner />;
  if (error) return <ErrorMessage message={error} onRetry={refetch} />;

  return (
    <div className="budget-plan-list">
      <div className="page-header">
        <h2>Piani Budget Annuali</h2>
        <Link to="/budget-plans/new" className="btn btn-primary">
          Nuovo Piano Budget
        </Link>
      </div>

      {/* Filters */}
      <div className="filters-section">
        <div className="filters-row">
          <div className="filter-group">
            <label htmlFor="plant-filter">Impianto:</label>
            <select
              id="plant-filter"
              value={filters.impiantoId || ''}
              onChange={(e) => setFilters(prev => ({
                ...prev,
                impiantoId: e.target.value ? parseInt(e.target.value) : undefined
              }))}
            >
              <option value="">Tutti gli impianti</option>
              {plants.map(plant => (
                <option key={plant.impiantoId} value={plant.impiantoId}>
                  {plant.nomeImpianto}
                </option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label htmlFor="year-filter">Anno:</label>
            <select
              id="year-filter"
              value={filters.annoBudget || ''}
              onChange={(e) => setFilters(prev => ({
                ...prev,
                annoBudget: e.target.value ? parseInt(e.target.value) : undefined
              }))}
            >
              <option value="">Tutti gli anni</option>
              {Array.from(new Set(plans.map(p => p.annoBudget)))
                .sort((a, b) => b - a)
                .map(year => (
                  <option key={year} value={year}>{year}</option>
                ))}
            </select>
          </div>

          <div className="filter-group">
            <label htmlFor="status-filter">Stato:</label>
            <select
              id="status-filter"
              value={filters.statoBudget !== undefined ? filters.statoBudget : ''}
              onChange={(e) => setFilters(prev => ({
                ...prev,
                statoBudget: e.target.value !== '' ? parseInt(e.target.value) as StatoBudget : undefined
              }))}
            >
              <option value="">Tutti gli stati</option>
              {Object.entries(STATO_BUDGET_LABELS).map(([value, label]) => (
                <option key={value} value={value}>{label}</option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label htmlFor="search-filter">Ricerca:</label>
            <input
              id="search-filter"
              type="text"
              placeholder="Cerca per nome impianto o note..."
              value={filters.searchText || ''}
              onChange={(e) => setFilters(prev => ({
                ...prev,
                searchText: e.target.value || undefined
              }))}
            />
          </div>

          <button 
            className="btn btn-secondary"
            onClick={() => setFilters({})}
          >
            Pulisci Filtri
          </button>
        </div>
      </div>

      {/* Results Summary */}
      <div className="results-summary">
        <p>Trovati {filteredPlans.length} piani budget</p>
      </div>

      {/* Budget Plans Table */}
      <div className="table-container">
        <table className="budget-plans-table">
          <thead>
            <tr>
              <th>Anno</th>
              <th>Impianto</th>
              <th>Stato</th>
              <th>Budget Totale</th>
              <th>Data Creazione</th>
              <th>Ultima Modifica</th>
              <th>Azioni</th>
            </tr>
          </thead>
          <tbody>
            {filteredPlans.length === 0 ? (
              <tr>
                <td colSpan={7} className="no-data">
                  Nessun piano budget trovato
                </td>
              </tr>
            ) : (
              filteredPlans.map(plan => (
                <tr key={plan.pianoBudgetId}>
                  <td className="year-cell">{plan.annoBudget}</td>
                  <td className="plant-cell">
                    <div>
                      <strong>{plan.impianto?.nomeImpianto}</strong>
                      <br />
                      <small>{plan.impianto?.codiceImpianto}</small>
                    </div>
                  </td>
                  <td className="status-cell">
                    <span className={`badge ${getStatusBadgeClass(plan.statoBudget)}`}>
                      {STATO_BUDGET_LABELS[plan.statoBudget]}
                    </span>
                  </td>
                  <td className="amount-cell">
                    {formatCurrency(calculateTotalBudget(plan))}
                  </td>
                  <td className="date-cell">
                    {new Date(plan.dataCreazione).toLocaleDateString('it-IT')}
                  </td>
                  <td className="date-cell">
                    {new Date(plan.dataUltimaModifica).toLocaleDateString('it-IT')}
                  </td>
                  <td className="actions-cell">
                    <div className="action-buttons">
                      <Link 
                        to={`/budget-plans/${plan.pianoBudgetId}`}
                        className="btn btn-sm btn-outline"
                        title="Visualizza dettagli"
                      >
                        Visualizza
                      </Link>
                      {plan.statoBudget === StatoBudget.Bozza && (
                        <Link 
                          to={`/budget-plans/${plan.pianoBudgetId}/edit`}
                          className="btn btn-sm btn-primary"
                          title="Modifica piano budget"
                        >
                          Modifica
                        </Link>
                      )}
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default BudgetPlanList;
