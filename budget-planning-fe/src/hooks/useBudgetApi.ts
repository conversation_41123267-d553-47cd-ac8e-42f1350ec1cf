import { useState, useEffect } from 'react';
import {
  PianoBudgetAnnuale,
  CategoriaBudget,
  Impianto,
  CreatePianoBudgetRequest,
  UpdatePianoBudgetRequest,
  CopyBudgetRequest,
  BudgetSummary,
  MonthlyTotals,
  TipoCategoria
} from '../types/budget.types';

const API_BASE_URL = import.meta.env.VITE_BUDGET_PLANNING_API_URL || 'http://localhost:5111';

class BudgetApiService {
  private async fetchWithAuth(url: string, options: RequestInit = {}) {
    const token = localStorage.getItem('authToken'); // Adjust based on your auth implementation
    
    const response = await fetch(`${API_BASE_URL}${url}`, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : '',
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API Error: ${response.status} - ${errorText}`);
    }

    return response.json();
  }

  // Piano Budget operations
  async getAllPlans(): Promise<PianoBudgetAnnuale[]> {
    return this.fetchWithAuth('/api/budget-planning/plans');
  }

  async getPlan(id: number): Promise<PianoBudgetAnnuale> {
    return this.fetchWithAuth(`/api/budget-planning/plans/${id}`);
  }

  async getPlansByPlant(impiantoId: number): Promise<PianoBudgetAnnuale[]> {
    return this.fetchWithAuth(`/api/budget-planning/plans/plant/${impiantoId}`);
  }

  async getPlansByYear(anno: number): Promise<PianoBudgetAnnuale[]> {
    return this.fetchWithAuth(`/api/budget-planning/plans/year/${anno}`);
  }

  async createPlan(plan: CreatePianoBudgetRequest): Promise<PianoBudgetAnnuale> {
    return this.fetchWithAuth('/api/budget-planning/plans', {
      method: 'POST',
      body: JSON.stringify(plan),
    });
  }

  async updatePlan(id: number, plan: UpdatePianoBudgetRequest): Promise<void> {
    return this.fetchWithAuth(`/api/budget-planning/plans/${id}`, {
      method: 'PUT',
      body: JSON.stringify(plan),
    });
  }

  async deletePlan(id: number): Promise<void> {
    return this.fetchWithAuth(`/api/budget-planning/plans/${id}`, {
      method: 'DELETE',
    });
  }

  async copyFromPreviousYear(request: CopyBudgetRequest): Promise<PianoBudgetAnnuale> {
    const { impiantoId, annoOrigine, annoDestinazione } = request;
    return this.fetchWithAuth(
      `/api/budget-planning/plans/${impiantoId}/copy?annoOrigine=${annoOrigine}&annoDestinazione=${annoDestinazione}`,
      { method: 'POST' }
    );
  }

  async submitForApproval(id: number): Promise<PianoBudgetAnnuale> {
    return this.fetchWithAuth(`/api/budget-planning/plans/${id}/submit`, {
      method: 'POST',
    });
  }

  async approvePlan(id: number, noteApprovazione?: string): Promise<PianoBudgetAnnuale> {
    return this.fetchWithAuth(`/api/budget-planning/plans/${id}/approve`, {
      method: 'POST',
      body: JSON.stringify(noteApprovazione),
    });
  }

  // Categories operations
  async getCategories(): Promise<CategoriaBudget[]> {
    return this.fetchWithAuth('/api/budget-planning/categories');
  }

  async getActiveCategories(): Promise<CategoriaBudget[]> {
    return this.fetchWithAuth('/api/budget-planning/categories/active');
  }

  async getCategoriesByType(tipo: TipoCategoria): Promise<CategoriaBudget[]> {
    return this.fetchWithAuth(`/api/budget-planning/categories/type/${tipo}`);
  }

  // Plants operations
  async getPlants(): Promise<Impianto[]> {
    return this.fetchWithAuth('/api/budget-planning/plants');
  }

  async getActivePlants(): Promise<Impianto[]> {
    return this.fetchWithAuth('/api/budget-planning/plants/active');
  }

  // Reports operations
  async getBudgetSummary(pianoBudgetId: number): Promise<BudgetSummary> {
    return this.fetchWithAuth(`/api/budget-planning/reports/summary/${pianoBudgetId}`);
  }

  async getMonthlyTotals(pianoBudgetId: number): Promise<number[]> {
    return this.fetchWithAuth(`/api/budget-planning/reports/monthly/${pianoBudgetId}`);
  }

  async getTotalBudget(pianoBudgetId: number): Promise<number> {
    return this.fetchWithAuth(`/api/budget-planning/reports/total/${pianoBudgetId}`);
  }
}

const budgetApiService = new BudgetApiService();

// Custom hooks for budget operations
export function useBudgetPlans() {
  const [plans, setPlans] = useState<PianoBudgetAnnuale[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPlans = async () => {
      try {
        setLoading(true);
        const data = await budgetApiService.getAllPlans();
        setPlans(data);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch budget plans');
      } finally {
        setLoading(false);
      }
    };

    fetchPlans();
  }, []);

  const refetch = async () => {
    try {
      setLoading(true);
      const data = await budgetApiService.getAllPlans();
      setPlans(data);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch budget plans');
    } finally {
      setLoading(false);
    }
  };

  return { plans, loading, error, refetch };
}

export function useBudgetPlan(id: number | null) {
  const [plan, setPlan] = useState<PianoBudgetAnnuale | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!id) {
      setPlan(null);
      return;
    }

    const fetchPlan = async () => {
      try {
        setLoading(true);
        const data = await budgetApiService.getPlan(id);
        setPlan(data);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch budget plan');
      } finally {
        setLoading(false);
      }
    };

    fetchPlan();
  }, [id]);

  return { plan, loading, error };
}

export function useBudgetCategories() {
  const [categories, setCategories] = useState<CategoriaBudget[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        const data = await budgetApiService.getActiveCategories();
        setCategories(data);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch categories');
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  return { categories, loading, error };
}

export function useBudgetPlants() {
  const [plants, setPlants] = useState<Impianto[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPlants = async () => {
      try {
        setLoading(true);
        const data = await budgetApiService.getActivePlants();
        setPlants(data);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch plants');
      } finally {
        setLoading(false);
      }
    };

    fetchPlants();
  }, []);

  return { plants, loading, error };
}

export { budgetApiService };
