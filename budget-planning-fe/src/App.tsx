import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import BudgetPlanList from './components/BudgetPlanList';
import BudgetPlanForm from './components/BudgetPlanForm';
import BudgetPlanDetail from './components/BudgetPlanDetail';
import './App.css';

function App() {
  return (
    <div className="budget-planning-app">
      <Router>
        <div className="container">
          <header className="app-header">
            <h1>Piano Budget Annuale di Impianto</h1>
            <p>Gestione dei piani budget annuali per gli impianti</p>
          </header>

          <main className="main-content">
            <Routes>
              <Route path="/" element={<Navigate to="/budget-plans" replace />} />
              <Route path="/budget-plans" element={<BudgetPlanList />} />
              <Route path="/budget-plans/new" element={<BudgetPlanForm />} />
              <Route path="/budget-plans/:id" element={<BudgetPlanDetail />} />
              <Route path="/budget-plans/:id/edit" element={<BudgetPlanForm />} />
            </Routes>
          </main>
        </div>
      </Router>
    </div>
  );
}

export default App;
