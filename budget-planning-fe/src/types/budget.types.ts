// Budget Planning TypeScript Types

export enum StatoBudget {
  Bozza = 0,
  InRevisione = 1,
  Approvato = 2,
  Archiviato = 3
}

export enum TipoCategoria {
  Ricavi = 0,
  Costi = 1,
  CapEx = 2
}

export enum StatoImpianto {
  Attivo = 0,
  Inattivo = 1,
  InManutenzione = 2
}

export enum StatoApprovazione {
  InAttesa = 0,
  Approvato = 1,
  Rifiutato = 2
}

export interface PianoBudgetAnnuale {
  pianoBudgetId: number;
  impiantoId: number;
  annoBudget: number;
  statoBudget: StatoBudget;
  dataCreazione: string;
  dataUltimaModifica: string;
  utenteCreazione: string;
  utenteUltimaModifica: string;
  versione: number;
  noteGenerali?: string;
  impianto?: Impianto;
  vociBudget: VoceBudget[];
  approvazioni: ApprovazioneBudget[];
}

export interface CategoriaBudget {
  categoriaId: number;
  codiceCategoria: string;
  nomeCategoria: string;
  tipoCategoria: TipoCategoria;
  categoriaPadreId?: number;
  ordineVisualizzazione: number;
  attiva: boolean;
  categoriaPadre?: CategoriaBudget;
  sottoCategorie: CategoriaBudget[];
  vociBudget: VoceBudget[];
}

export interface VoceBudget {
  voceBudgetId: number;
  pianoBudgetId: number;
  categoriaId: number;
  codiceVoce: string;
  descrizioneVoce: string;
  importoAnnuale: number;
  importoGen: number;
  importoFeb: number;
  importoMar: number;
  importoApr: number;
  importoMag: number;
  importoGiu: number;
  importoLug: number;
  importoAgo: number;
  importoSet: number;
  importoOtt: number;
  importoNov: number;
  importoDic: number;
  noteVoce?: string;
  dataCreazione: string;
  dataModifica: string;
  pianoBudget?: PianoBudgetAnnuale;
  categoria?: CategoriaBudget;
}

export interface Impianto {
  impiantoId: number;
  codiceImpianto: string;
  nomeImpianto: string;
  tipoImpianto: string;
  ubicazione: string;
  capacitaProduttiva?: number;
  dataAttivazione: string;
  statoImpianto: StatoImpianto;
  pianiBudget: PianoBudgetAnnuale[];
}

export interface ApprovazioneBudget {
  approvazioneId: number;
  pianoBudgetId: number;
  utenteApprovatore: string;
  dataApprovazione: string;
  statoApprovazione: StatoApprovazione;
  noteApprovazione?: string;
  pianoBudget?: PianoBudgetAnnuale;
}

// API Response Types
export interface BudgetSummary {
  [key: string]: number; // TipoCategoria as key, total amount as value
}

export interface MonthlyTotals {
  [month: number]: number; // Month index (0-11) as key, total amount as value
}

// Form Types
export interface CreatePianoBudgetRequest {
  impiantoId: number;
  annoBudget: number;
  noteGenerali?: string;
  vociBudget: CreateVoceBudgetRequest[];
}

export interface CreateVoceBudgetRequest {
  categoriaId: number;
  codiceVoce: string;
  descrizioneVoce: string;
  importoAnnuale: number;
  importoGen: number;
  importoFeb: number;
  importoMar: number;
  importoApr: number;
  importoMag: number;
  importoGiu: number;
  importoLug: number;
  importoAgo: number;
  importoSet: number;
  importoOtt: number;
  importoNov: number;
  importoDic: number;
  noteVoce?: string;
}

export interface UpdatePianoBudgetRequest extends CreatePianoBudgetRequest {
  pianoBudgetId: number;
  versione: number;
}

export interface CopyBudgetRequest {
  impiantoId: number;
  annoOrigine: number;
  annoDestinazione: number;
}

// UI State Types
export interface BudgetPlanListFilters {
  impiantoId?: number;
  annoBudget?: number;
  statoBudget?: StatoBudget;
  searchText?: string;
}

export interface BudgetFormState {
  isLoading: boolean;
  isSubmitting: boolean;
  errors: Record<string, string>;
  isDirty: boolean;
}

// Utility Types
export const MONTH_NAMES = [
  'Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno',
  'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'
] as const;

export const STATO_BUDGET_LABELS: Record<StatoBudget, string> = {
  [StatoBudget.Bozza]: 'Bozza',
  [StatoBudget.InRevisione]: 'In Revisione',
  [StatoBudget.Approvato]: 'Approvato',
  [StatoBudget.Archiviato]: 'Archiviato'
};

export const TIPO_CATEGORIA_LABELS: Record<TipoCategoria, string> = {
  [TipoCategoria.Ricavi]: 'Ricavi',
  [TipoCategoria.Costi]: 'Costi',
  [TipoCategoria.CapEx]: 'Investimenti'
};

export const STATO_IMPIANTO_LABELS: Record<StatoImpianto, string> = {
  [StatoImpianto.Attivo]: 'Attivo',
  [StatoImpianto.Inattivo]: 'Inattivo',
  [StatoImpianto.InManutenzione]: 'In Manutenzione'
};
