import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { federation } from "@module-federation/vite";

// Template for Vite configuration with Module Federation
// Replace budgetplanning with your actual service name
// Replace 3007 with your service port

export default defineConfig({
  plugins: [
    react(),
    federation({
      name: "budgetplanningFe",
      filename: "remoteEntry.js",
      exposes: {
        "./App": "./src/App.tsx",
        // Add more exposed components as needed
        // "./Component": "./src/components/Component.tsx",
      },
      shared: {
        react: {
          singleton: true,
          requiredVersion: "^18.0.0",
        },
        "react-dom": {
          singleton: true,
          requiredVersion: "^18.0.0",
        },
        "react-router-dom": {
          singleton: true,
          requiredVersion: "^7.0.0",
        },
      },
    }),
  ],
  server: {
    port: 3007, // Replace with actual port number
    cors: true,
    host: true, // Allow external connections
  },
  build: {
    target: "esnext",
    rollupOptions: {
      external: ["react", "react-dom"],
    },
  },
  preview: {
    port: 3007,
    cors: true,
    host: true,
  },
});
