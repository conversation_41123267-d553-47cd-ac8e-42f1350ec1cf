{"name": "budget-planning-fe", "private": true, "version": "1.0.0", "type": "module", "description": "budgetplanning micro-frontend application", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.6.3"}, "devDependencies": {"@eslint/js": "^9.13.0", "@module-federation/vite": "^1.6.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "eslint": "^9.13.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "typescript": "~5.6.2", "typescript-eslint": "^8.10.0", "vite": "^6.0.1"}, "peerDependencies": {"react": "^18.3.1", "react-dom": "^18.3.1"}}