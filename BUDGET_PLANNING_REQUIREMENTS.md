# Piano Budget Annuale di Impianto - Functional Requirements

## 📋 Overview

The "Creazione Piano Budget Annuale di Impianto" (Annual Plant Budget Plan Creation) functionality allows users to create, manage, and track annual budget plans for industrial plants and facilities.

## 🎯 Core Functionality Requirements

### 1. Budget Plan Management
- **Create new annual budget plans** for specific plants/facilities
- **Edit existing budget plans** (with proper versioning)
- **Copy budget plans** from previous years as templates
- **Delete budget plans** (with proper authorization)
- **View budget plan history** and versions

### 2. Budget Categories and Line Items
- **Revenue Categories**:
  - Energy production revenue
  - Service fees
  - Other operational income
  
- **Cost Categories**:
  - Personnel costs
  - Maintenance and repairs
  - Energy consumption
  - Raw materials
  - Equipment depreciation
  - Insurance and permits
  - Other operational expenses

- **Capital Expenditures**:
  - Equipment purchases
  - Infrastructure improvements
  - Technology upgrades

### 3. Budget Planning Features
- **Monthly breakdown** of annual budget
- **Multi-year comparison** (current vs previous years)
- **Budget variance analysis** (planned vs actual)
- **Automatic calculations** (totals, percentages, variances)
- **Budget approval workflow** (draft → review → approved)
- **Comments and notes** for each budget line

### 4. Plant/Facility Management
- **Plant selection** from available facilities
- **Plant-specific budget templates**
- **Multi-plant budget consolidation**
- **Plant performance metrics integration**

## 📊 Data Model Requirements

### Core Entities

#### 1. Budget Plan (Piano Budget)
```sql
t_piano_budget_annuale
- piano_budget_id (PK)
- impianto_id (FK to plants)
- anno_budget (year)
- stato_budget (draft/review/approved/archived)
- data_creazione
- data_ultima_modifica
- utente_creazione
- utente_ultima_modifica
- versione
- note_generali
```

#### 2. Budget Categories (Categorie Budget)
```sql
t_categorie_budget
- categoria_id (PK)
- codice_categoria
- nome_categoria
- tipo_categoria (revenue/cost/capex)
- categoria_padre_id (for hierarchical structure)
- ordine_visualizzazione
- attiva
```

#### 3. Budget Line Items (Voci Budget)
```sql
t_voci_budget
- voce_budget_id (PK)
- piano_budget_id (FK)
- categoria_id (FK)
- codice_voce
- descrizione_voce
- importo_annuale
- importo_gen, importo_feb, ..., importo_dic (monthly breakdown)
- note_voce
- data_creazione
- data_modifica
```

#### 4. Plants/Facilities (Impianti)
```sql
t_impianti
- impianto_id (PK)
- codice_impianto
- nome_impianto
- tipo_impianto
- ubicazione
- capacita_produttiva
- data_attivazione
- stato_impianto (active/inactive/maintenance)
```

#### 5. Budget Approvals (Approvazioni Budget)
```sql
t_approvazioni_budget
- approvazione_id (PK)
- piano_budget_id (FK)
- utente_approvatore
- data_approvazione
- stato_approvazione (pending/approved/rejected)
- note_approvazione
```

## 🔧 Technical Requirements

### API Endpoints
```
GET    /api/budget-planning/plans                    # List all budget plans
GET    /api/budget-planning/plans/{id}               # Get specific budget plan
POST   /api/budget-planning/plans                    # Create new budget plan
PUT    /api/budget-planning/plans/{id}               # Update budget plan
DELETE /api/budget-planning/plans/{id}               # Delete budget plan

GET    /api/budget-planning/categories               # Get budget categories
GET    /api/budget-planning/plants                   # Get available plants
GET    /api/budget-planning/templates/{year}         # Get budget template

POST   /api/budget-planning/plans/{id}/copy          # Copy from previous year
POST   /api/budget-planning/plans/{id}/submit        # Submit for approval
POST   /api/budget-planning/plans/{id}/approve       # Approve budget plan

GET    /api/budget-planning/reports/variance         # Budget variance reports
GET    /api/budget-planning/reports/summary          # Budget summary reports
```

### Frontend Components
- **Budget Plan List** - Table showing all budget plans with filters
- **Budget Plan Form** - Create/edit budget plan with validation
- **Budget Line Items Grid** - Editable grid for budget entries
- **Monthly Breakdown View** - Monthly distribution of budget items
- **Budget Summary Dashboard** - Charts and KPIs
- **Approval Workflow** - Status tracking and approval actions
- **Import/Export Tools** - Excel import/export functionality

## 🎨 User Interface Requirements

### 1. Budget Plan List View
- Filterable table with columns: Year, Plant, Status, Total Budget, Created Date
- Actions: Create New, Edit, Copy, Delete, View Details
- Search and filter capabilities
- Export to Excel functionality

### 2. Budget Plan Creation/Edit Form
- Plant selection dropdown
- Year selection
- Budget status indicator
- Tabbed interface:
  - **General Info**: Basic plan details
  - **Revenue**: Revenue line items with monthly breakdown
  - **Costs**: Cost line items with monthly breakdown
  - **CapEx**: Capital expenditure items
  - **Summary**: Totals and variance analysis
  - **Approval**: Workflow status and comments

### 3. Budget Line Items Grid
- Editable grid with columns: Category, Description, Annual Amount, Monthly Breakdown
- Add/Remove rows functionality
- Automatic calculation of totals
- Validation for required fields and numeric values
- Copy/paste from Excel support

### 4. Dashboard and Reports
- Budget summary charts (pie charts for categories, bar charts for monthly trends)
- Variance analysis (planned vs actual)
- Multi-year comparison charts
- KPI indicators (total budget, variance %, approval status)

## 🔐 Security and Authorization

### User Roles
- **Budget Creator**: Can create and edit draft budgets
- **Budget Reviewer**: Can review and comment on budgets
- **Budget Approver**: Can approve/reject budgets
- **Budget Viewer**: Read-only access to approved budgets
- **System Admin**: Full access to all budget data

### Permissions
- Users can only access budgets for plants they are authorized for
- Draft budgets can only be edited by creators
- Approved budgets are read-only (except for authorized users)
- Audit trail for all budget modifications

## 📈 Business Rules

### Validation Rules
- Budget year must be current year or future year
- Total budget amounts must be positive
- Monthly breakdown must sum to annual total
- Required fields: Plant, Year, at least one budget line item
- Budget categories must be valid and active

### Workflow Rules
- New budgets start in "Draft" status
- Only complete budgets can be submitted for approval
- Approved budgets cannot be modified (new version required)
- Budget approval requires proper authorization level
- Automatic notifications for approval requests

### Calculation Rules
- Automatic calculation of category totals
- Automatic calculation of monthly totals
- Variance calculation: (Actual - Planned) / Planned * 100
- Currency formatting and rounding rules
- Exchange rate handling for multi-currency scenarios

## 🔄 Integration Requirements

### Data Integration
- Integration with plant/facility master data
- Integration with accounting systems for actual vs planned analysis
- Integration with user management for authorization
- Integration with notification systems for workflow alerts

### Import/Export
- Excel template download for budget data entry
- Excel import with validation and error reporting
- PDF export for budget reports and approvals
- CSV export for data analysis

This comprehensive functionality will provide a complete budget planning solution integrated with the existing microservices platform.
