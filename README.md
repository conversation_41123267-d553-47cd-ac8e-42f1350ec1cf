# NDUE Next Generation Microservices Platform

A modern microservices platform using .NET 9 APIs with React frontends, featuring EntraID authentication and Module Federation for seamless micro-frontend integration.

## Quick Start

### Prerequisites

- **Node.js 18+** and **npm** for frontend development
- **.NET 9 SDK** for backend APIs
- **PostgreSQL** database access
- **Azure AD / EntraID** tenant for authentication

### 1. Setup Environment Variables

```bash
# Copy template to create local environment configuration
cp config.env.template config.env.local

# Edit with your database credentials and authentication settings
nano config.env.local
```

#### Configuration Files

- `config.env.local` - Local development environment (ignored by git)
- `config.env.template` - Template for creating new environments

#### Required Variables

**PostgreSQL Database:**

```bash
PGHOST=your-postgres-host.com
PGUSER=your-username
PGPORT=5432
PGDATABASE=your-database-name
PGPASSWORD=your-password
```

**Authentication (EntraID):**

```bash
VITE_PORTAL_CLIENT_ID=your-portal-spa-client-id
VITE_TENANT_ID=your-tenant-id
VITE_AUTH_API_CLIENT_ID=your-auth-api-client-id
AZURE_TENANT_ID=your-tenant-id
AZURE_CLIENT_ID=your-auth-api-client-id
```

**📖 For detailed setup guides:**

- [AUTHENTICATION_SETUP.md](AUTHENTICATION_SETUP.md) - Complete authentication configuration
- [ENVIRONMENT_SETUP.md](ENVIRONMENT_SETUP.md) - Environment configuration details
- [DEPLOYMENT_CONFIG_GUIDE.md](DEPLOYMENT_CONFIG_GUIDE.md) - Production deployment guide

**Note:** All `config.env.*` files are ignored by git to keep credentials secure.

### Running the Application

1. **Install dependencies for all frontend projects:**

   ```bash
   cd portal && npm install && cd ..
   cd gruppi-fe && npm install && cd ..
   cd eventi-teleriscaldamento-fe && npm install && cd ..
   ```

2. **Start all services:**

   ```bash
   ./start-local.sh
   ```

   This starts 6 services in parallel with automatic monitoring and health checks.

   Or manually start each service:

   ```bash
   # Terminal 1 - Auth API
   cd autho-api
   dotnet run --environment Local

   # Terminal 2 - Gruppi API
   cd gruppi-api
   dotnet run --project GruppiService.Api --environment Local

   # Terminal 3 - Eventi API
   cd eventi-teleriscaldamento-api
   dotnet run --project EventiTeleriscaldamentoService.Api --environment Local

   # Terminal 4 - Portal
   cd portal
   npm run dev

   # Terminal 5 - Gruppi Frontend
   cd gruppi-fe
   npm run dev

   # Terminal 6 - Eventi Frontend
   cd eventi-teleriscaldamento-fe
   npm run dev
   ```

### Access Points

- **Portal Shell**: http://localhost:3000 (🔐 **Requires Authentication**)
- **Auth API**: http://localhost:8003 (Token validation service)
- **Gruppi Frontend**: http://localhost:3002 (standalone)
- **Eventi Frontend**: http://localhost:3005 (standalone)
- **Gruppi API**: http://localhost:5108 + [Swagger](http://localhost:5108/swagger)
- **Eventi API**: http://localhost:5109 + [Swagger](http://localhost:5109/swagger)

## 🔐 Authentication Architecture

The platform uses **Azure AD / EntraID** for authentication with a dedicated Auth API:

- **Portal**: Handles user authentication via MSAL.js and shares tokens
- **Auth API**: Validates EntraID tokens for all business APIs
- **Business APIs**: Validate tokens by calling the Auth API
- **Frontends**: Inherit authentication from Portal via localStorage

**Two App Registrations Required:**

1. **Portal SPA** (`VITE_PORTAL_CLIENT_ID`) - Single-page application for frontend auth
2. **Auth API** (`AZURE_CLIENT_ID`) - Web API for token validation

## Features

### Gruppi Service

- **Frontend**: React component with blue theme that displays data from `app.t_gruppi` table
- **Backend**: .NET 9 API with Entity Framework Core 9.0.1
- **Database**: Connects to PostgreSQL `app.t_gruppi` table
- **Architecture**: Clean Architecture with Repository Pattern
- **Components**: Uses shared UI components for consistent UX

### Eventi Teleriscaldamento Service

- **Frontend**: React component with green theme that displays data from `app.t_skc_eventiteleriscaldamento` table
- **Backend**: .NET 9 API with Entity Framework Core 9.0.1
- **Database**: Connects to PostgreSQL `app.t_skc_eventiteleriscaldamento` table
- **Architecture**: Clean Architecture with Repository Pattern
- **Components**: Uses shared UI components for consistent UX

### Portal (Shell Application)

- **Framework**: React 18 + TypeScript + Vite
- **Authentication**: MSAL.js integration with EntraID
- **Module Federation**: Loads micro-frontends dynamically
- **Navigation**: Unified navigation between microservices
- **Token Management**: Centralized authentication for all services

### Auth API Service

- **Framework**: .NET 9 minimal API
- **Purpose**: EntraID token validation for all business APIs
- **Endpoints**: `/api/auth/validate-token`, `/api/auth/user-info`
- **Security**: Public key validation, no client secrets stored

### Shared Component Architecture

- **DataTable**: Reusable table component with theme support (blue/green)
- **LoadingSpinner**: Consistent loading states
- **ErrorMessage**: Standardized error handling
- **EmptyState**: User-friendly empty data states
- **PageContainer**: Consistent page layouts
- **useApiData**: Custom hook for data fetching eliminating code duplication
- **Formatters**: Date, number, and nullable value formatting utilities

### Module Federation Integration

- The portal loads micro-frontends dynamically
- Navigate to `/gruppi` or `/eventi-teleriscaldamento` in the portal
- Hot module reloading works across micro-frontends
- Each service maintains its own theme and branding

## Development

### Project Structure

```
ndue-newgen/
├── portal/                           # Shell application with authentication
├── autho-api/                        # Authentication API for token validation
├── gruppi-api/                       # Gruppi business API
├── gruppi-fe/                        # Gruppi micro-frontend
├── eventi-teleriscaldamento-api/     # Eventi business API
├── eventi-teleriscaldamento-fe/      # Eventi micro-frontend
├── shared/
│   ├── ui-components/               # Shared React components
│   ├── api-contracts/               # TypeScript type definitions
│   └── utilities/                   # Common utilities
├── infrastructure/                   # Deployment configurations
├── config.env.template              # Environment template
├── start-local.sh                   # Local development startup script
└── *.md                             # Documentation files
```

### Adding New Microservices

1. **Create API structure:**

   ```
   new-service-api/
   ├── NewService.Api/              # Controllers, Program.cs
   ├── NewService.Application/      # Business logic, services
   ├── NewService.Domain/           # Entities, interfaces
   └── NewService.Infrastructure/   # Repository, DbContext
   ```

2. **Create Frontend structure:**

   ```
   new-service-fe/
   ├── src/
   │   ├── components/
   │   ├── App.tsx
   │   └── main.tsx
   ├── vite.config.ts               # Module Federation config
   └── package.json
   ```

3. **Integration steps:**
   - Add authentication validation in API using Auth API
   - Configure Module Federation in frontend
   - Add remote to portal's `vite.config.ts`
   - Add route in portal's `App.tsx`
   - Update `start-local.sh` to include new services

### Database Schema

The application uses PostgreSQL with schema `app`:

**Gruppi Table:**

```sql
SELECT gruppi_id, unitaproduttive_id, codice_gruppo, descrizionegruppo, tipogruppo_id
FROM app.t_gruppi;
```

**Eventi Teleriscaldamento Table:**

```sql
SELECT skc_eventiteleriscaldamento_id, sk_conduzione_id, dataoraevento,
       tipoeventocaldaia, quantitativocalore, eventocomplementare_id
FROM app.t_skc_eventiteleriscaldamento;
```

## Project Structure Details

### .NET API (Clean Architecture)

- **Api**: Controllers, Program.cs, configuration
- **Application**: Services, business logic
- **Domain**: Entities, interfaces, value objects
- **Infrastructure**: Repository implementations, DbContext, external services

### React Micro-frontends

- **Module Federation**: Each micro-frontend exposes components
- **Shared Dependencies**: React and React-DOM are singletons
- **Independent Development**: Each team can develop independently
- **Hot Reloading**: Changes reflect immediately in the portal
- **Shared Components**: Compile-time imports from `/shared/ui-components`

### Code Reuse Stats

After implementing shared components:

- **~400 lines of duplicate code eliminated**
- **Consistent theming** across all microservices
- **Standardized data fetching** with useApiData hook
- **Unified error handling** and loading states
- **Centralized formatters** for dates, numbers, and nullable values

## Service Ports

| Service         | Development Port | Purpose                          |
| --------------- | ---------------- | -------------------------------- |
| Portal          | 3000             | Main shell application           |
| Gruppi Frontend | 3002             | Gruppi micro-frontend standalone |
| Eventi Frontend | 3005             | Eventi micro-frontend standalone |
| Gruppi API      | 5108             | Gruppi service API               |
| Eventi API      | 5109             | Eventi service API               |
