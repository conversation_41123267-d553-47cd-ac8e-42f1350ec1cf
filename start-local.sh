#!/bin/bash

# Default environment
ENVIRONMENT=${1:-local}

echo "Starting microservices development environment for: $ENVIRONMENT"

# Load environment variables from config.env.{environment} if it exists
CONFIG_FILE="config.env.$ENVIRONMENT"
if [ -f "$CONFIG_FILE" ]; then
    echo "📄 Loading environment variables from $CONFIG_FILE..."
    export $(cat "$CONFIG_FILE" | grep -v '^#' | xargs)
elif [ -f "config.env" ]; then
    echo "📄 Loading environment variables from config.env (legacy)..."
    export $(cat config.env | grep -v '^#' | xargs)
else
    echo "⚠️  No configuration file found for environment '$ENVIRONMENT'."
    echo "   Available environments: dev, test, qa, prod"
    echo "   Create $CONFIG_FILE or use: ./start-dev.sh [environment]"
    echo "   Example: ./start-dev.sh dev"
    exit 1
fi

# Function to kill process on a specific port
kill_port() {
    local port=$1
    local pid=$(lsof -ti:$port)
    echo "port $port is used by pid $pid"
    if [ ! -z "$pid" ]; then
        echo "🚑 Killing process on port $port (PID: $pid)"
        kill -9 $pid 2>/dev/null
        sleep 1
    fi
}

# Kill any processes on our required ports
echo "🧹 Cleaning up ports..."
kill_port ${PORTAL_PORT:-3000}  # Portal
kill_port ${GRUPPI_FRONTEND_PORT:-3002}  # Gruppi Frontend
kill_port ${EVENTI_FRONTEND_PORT:-3005}  # Eventi Teleriscaldamento Frontend
kill_port ${GRUPPI_API_PORT:-5108}  # Gruppi API HTTP
kill_port ${EVENTI_API_PORT:-5109}  # Eventi Teleriscaldamento API HTTP
kill_port ${AUTH_API_PORT:-8003}  # Auth API HTTP

# Verify required environment variables are set
if [ -z "$PGHOST" ] || [ -z "$PGUSER" ] || [ -z "$PGPASSWORD" ] || [ -z "$PGDATABASE" ]; then
    echo "❌ Error: Required PostgreSQL environment variables are not set."
    echo "   Please ensure $CONFIG_FILE exists and contains:"
    echo "   - PGHOST"
    echo "   - PGUSER" 
    echo "   - PGPASSWORD"
    echo "   - PGDATABASE"
    echo "   - PGPORT (optional, defaults to 5432)"
    exit 1
fi

echo "✅ Environment: $ENVIRONMENT"
echo "✅ Database configuration loaded:"
echo "   Host: $PGHOST"
echo "   Database: $PGDATABASE"
echo "   User: $PGUSER"
echo "   Port: ${PGPORT:-5432}"
echo "   Log Level: ${LOG_LEVEL:-Information}"

# Function to monitor service startup
monitor_service() {
    local name=$1
    local url=$2
    local max_attempts=10
    local attempt=1
    
    echo "⏳ Monitoring $name startup..."
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" >/dev/null 2>&1; then
            echo "✅ $name is running at $url"
            return 0
        fi
        echo "   Attempt $attempt/$max_attempts - waiting..."
        sleep 2
        attempt=$((attempt + 1))
    done
    echo "⚠️  $name may still be starting up at $url"
    return 1
}

# Store base directory
BASE_DIR=$(pwd)

# Start the Auth API (in background)
echo "🚀 Starting Auth API..."
cd "$BASE_DIR/autho-api"
echo "   Using EntraID authentication..."
dotnet run --urls="http://localhost:${AUTH_API_PORT}" --environment Local >/dev/null 2>&1 &
AUTH_API_PID=$!
echo "   Auth API PID: $AUTH_API_PID"

# Start the Gruppi API (in background)
echo "🚀 Starting Gruppi API..."
cd "$BASE_DIR/gruppi-api"
echo "   Using PostgreSQL database..."
dotnet run --project GruppiService.Api --environment Local >/dev/null 2>&1 &
GRUPPI_API_PID=$!
echo "   Gruppi API PID: $GRUPPI_API_PID"

# Start the Eventi Teleriscaldamento API (in background)
echo "🚀 Starting Eventi Teleriscaldamento API..."
cd "$BASE_DIR/eventi-teleriscaldamento-api"
echo "   Using PostgreSQL database..."
dotnet run --project EventiTeleriscaldamentoService.Api --environment Local >/dev/null 2>&1 &
EVENTI_API_PID=$!
echo "   Eventi API PID: $EVENTI_API_PID"

# Monitor API startup
monitor_service "Auth API" "http://localhost:${AUTH_API_PORT}/health"
monitor_service "Gruppi API" "http://localhost:${GRUPPI_API_PORT}/api/gruppi"
monitor_service "Eventi API" "http://localhost:${EVENTI_API_PORT}/api/eventi-teleriscaldamento"

# Start the Portal (in background)
echo "🚀 Starting Portal..."
cd "$BASE_DIR/portal"
npm run dev >/dev/null 2>&1 &
PORTAL_PID=$!
echo "   Portal PID: $PORTAL_PID"

# Start the NEW Gruppi Frontend (in background)
echo "🚀 Starting Gruppi Frontend (NEW)..."
cd "$BASE_DIR/gruppi-fe"
npm run dev >/dev/null 2>&1 &
GRUPPI_FRONTEND_PID=$!
echo "   Gruppi Frontend PID: $GRUPPI_FRONTEND_PID"

# Start the Eventi Teleriscaldamento Frontend (in background)
echo "🚀 Starting Eventi Teleriscaldamento Frontend..."
cd "$BASE_DIR/eventi-teleriscaldamento-fe"
npm install > /dev/null 2>&1
npm run dev >/dev/null 2>&1 &
EVENTI_FRONTEND_PID=$!
echo "   Eventi Frontend PID: $EVENTI_FRONTEND_PID"

# Monitor frontend services
sleep 3
monitor_service "Portal" "http://localhost:${PORTAL_PORT}"
monitor_service "Gruppi Frontend" "http://localhost:${GRUPPI_FRONTEND_PORT}"
monitor_service "Eventi Frontend" "http://localhost:${EVENTI_FRONTEND_PORT}"

echo ""
echo "🎉 All services started successfully!"
echo ""
echo "📊 Portal: http://localhost:${PORTAL_PORT}"
echo "🔐 Auth Service: http://localhost:${AUTH_API_PORT}"
echo "📋 Gruppi Service Frontend: ${GRUPPI_FRONTEND_URL}"
echo "🌡️ Eventi Teleriscaldamento Frontend: ${EVENTI_FRONTEND_URL}"
echo "🔧 Gruppi Service API: ${GRUPPI_API_URL}"
echo "🔥 Eventi Teleriscaldamento API: ${EVENTI_API_URL}"
echo "📖 Auth API Documentation: http://localhost:${AUTH_API_PORT}/swagger"
echo "📖 Gruppi API Documentation: ${GRUPPI_API_DOCS_URL}"
echo "📖 Eventi API Documentation: ${EVENTI_API_DOCS_URL}"
echo ""
echo "🔍 To check service status: ps aux | grep -E 'dotnet|vite'"
echo "🚑 Press Ctrl+C to stop all services"
echo ""

# Wait for user to press Ctrl+C
trap 'echo ""; echo "🚑 Stopping all services..."; kill $AUTH_API_PID $GRUPPI_API_PID $EVENTI_API_PID $PORTAL_PID $GRUPPI_FRONTEND_PID $EVENTI_FRONTEND_PID 2>/dev/null; echo "✅ All services stopped"; exit' INT
wait