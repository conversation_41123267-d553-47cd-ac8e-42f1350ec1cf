import React from 'react'
import './PageContainer.css'

interface PageContainerProps {
  title: string
  maxWidth?: string
  children: React.ReactNode
  className?: string
}

const PageContainer: React.FC<PageContainerProps> = ({ 
  title, 
  maxWidth = '1200px', 
  children, 
  className = '' 
}) => {
  return (
    <div className={`page-container ${className}`} style={{ maxWidth }}>
      <h2 className="page-title">{title}</h2>
      {children}
    </div>
  )
}

export default PageContainer