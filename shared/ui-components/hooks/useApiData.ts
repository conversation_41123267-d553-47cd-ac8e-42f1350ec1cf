import { useState, useEffect } from "react";

interface UseApiDataProps {
  endpoint: string;
  apiUrl?: string;
}

interface UseApiDataResult<T> {
  data: T[];
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

// Helper function to get authentication token from localStorage
const getAuthToken = (): string | null => {
  // Try to get the Auth API token first (for business API calls)
  // This is the token the business APIs are configured to accept
  const authApiToken = localStorage.getItem("ndue_auth_api_token");
  if (authApiToken) {
    console.log("Using Auth API token for business API call");
    return authApiToken;
  }

  // Fallback to basic access token (though business APIs might not accept this)
  const accessToken = localStorage.getItem("ndue_access_token");
  if (accessToken) {
    console.log("Using basic access token as fallback");
    return accessToken;
  }

  console.warn("No authentication token available");
  return null;
};

// Helper function to get API base URL
const getApiBaseUrl = (providedUrl?: string): string => {
  if (providedUrl) return providedUrl;

  // Try to get from environment variables (if available)
  if (typeof window !== "undefined" && (window as any).VITE_API_URL) {
    return (window as any).VITE_API_URL;
  }

  // No default fallback - each service should provide its own fallback URL
  throw new Error(
    "API URL not provided. Please specify apiUrl parameter or set VITE_API_URL environment variable."
  );
};

export function useApiData<T = any>({
  endpoint,
  apiUrl,
}: UseApiDataProps): UseApiDataResult<T> {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      const baseUrl = getApiBaseUrl(apiUrl);

      // Get authentication token
      const token = getAuthToken();

      // Build headers
      const headers: HeadersInit = {
        "Content-Type": "application/json",
      };

      // Add authorization header if token is available
      if (token) {
        headers["Authorization"] = `Bearer ${token}`;
      }

      console.log(`Making authenticated request to: ${baseUrl}${endpoint}`);
      console.log(`Token available: ${!!token}`);

      const response = await fetch(`${baseUrl}${endpoint}`, {
        method: "GET",
        headers,
        credentials: "include", // Include credentials for CORS
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error("Authentication required. Please login again.");
        }
        throw new Error(
          `HTTP error! status: ${response.status} - ${response.statusText}`
        );
      }

      const result = await response.json();
      setData(result);
      console.log(
        `Successfully fetched ${result.length} items from ${endpoint}`
      );
    } catch (err) {
      console.error("API call failed:", err);
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [endpoint, apiUrl]);

  return {
    data,
    loading,
    error,
    refetch: fetchData,
  };
}
