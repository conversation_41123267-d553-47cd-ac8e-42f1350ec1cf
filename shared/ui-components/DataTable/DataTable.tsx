import React from 'react'
import './DataTable.css'

interface Column {
  key: string
  header: string
  render?: (value: any, item: any) => React.ReactNode
}

interface DataTableProps {
  columns: Column[]
  data: any[]
  className?: string
  theme?: 'blue' | 'green'
}

const DataTable: React.FC<DataTableProps> = ({ 
  columns, 
  data, 
  className = '', 
  theme = 'blue' 
}) => {
  return (
    <div className="table-container">
      <table className={`data-table data-table--${theme} ${className}`}>
        <thead>
          <tr>
            {columns.map((column) => (
              <th key={column.key}>{column.header}</th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.map((item, index) => (
            <tr key={item.id || index}>
              {columns.map((column) => (
                <td key={column.key}>
                  {column.render 
                    ? column.render(item[column.key], item)
                    : item[column.key]
                  }
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

export default DataTable