.table-container {
  overflow-x: auto;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  background: white;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.data-table th {
  color: white;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
}

.data-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e2e8f0;
  color: #1f2937;
}

.data-table tbody tr {
  background-color: white;
  color: #1f2937;
}

.data-table tbody tr:hover {
  background-color: #f8fafc;
}

.data-table tbody tr:nth-child(even) {
  background-color: #f1f5f9;
}

/* Blue theme (Gruppi) */
.data-table--blue th {
  background-color: #646cff;
  border-bottom: 2px solid #5a67d8;
}

.data-table--blue tbody tr:hover {
  background-color: #f8fafc;
}

.data-table--blue tbody tr:nth-child(even) {
  background-color: #f1f5f9;
}

/* Green theme (Eventi) */
.data-table--green th {
  background-color: #059669;
  border-bottom: 2px solid #047857;
}

.data-table--green tbody tr:hover {
  background-color: #f0fdf4;
}

.data-table--green tbody tr:nth-child(even) {
  background-color: #f7fee7;
}