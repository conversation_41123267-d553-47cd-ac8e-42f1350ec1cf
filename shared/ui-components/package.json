{"name": "@ndue/ui-components", "version": "1.0.0", "type": "module", "engines": {"node": ">=24.3.0"}, "main": "./index.ts", "exports": {".": "./index.ts", "./DataTable": "./DataTable/DataTable.tsx", "./LoadingSpinner": "./LoadingSpinner/LoadingSpinner.tsx", "./ErrorMessage": "./ErrorMessage/ErrorMessage.tsx", "./EmptyState": "./EmptyState/EmptyState.tsx", "./PageContainer": "./PageContainer/PageContainer.tsx", "./hooks/useApiData": "./hooks/useApiData.ts"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "typescript": "~5.8.3"}}