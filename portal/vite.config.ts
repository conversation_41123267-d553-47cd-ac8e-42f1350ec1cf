import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { federation } from "@module-federation/vite";

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),
    federation({
      name: "portal",
      remotes: {
        gruppiFrontendService: {
          type: "module",
          name: "gruppiFrontendService",
          entry: "http://localhost:3002/remoteEntry.js",
          entryGlobalName: "gruppiFrontendService",
          shareScope: "default",
        },
        eventiTeleriscaldamentoFe: {
          type: "module",
          name: "eventiTeleriscaldamentoFe",
          entry: "http://localhost:3005/remoteEntry.js",
          entryGlobalName: "eventiTeleriscaldamentoFe",
          shareScope: "default",
        },
      },
      shared: {
        react: {
          singleton: true,
          requiredVersion: "^18.0.0",
        },
        "react-dom": {
          singleton: true,
          requiredVersion: "^18.0.0",
        },
        "react-router-dom": {
          singleton: true,
          requiredVersion: "^7.0.0",
        },
      },
    }),
  ],
  server: {
    port: 3000,
    cors: true,
  },
  build: {
    target: "esnext",
  },
});
