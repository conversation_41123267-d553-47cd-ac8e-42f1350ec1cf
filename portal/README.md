# NDUE Portal Frontend

Portal principale per l'autenticazione e la gestione dei microservizi NDUE.

## Configurazione Environment

Usa il sistema di configurazione centralizzato nella root del progetto:

```bash
# Copia il template di configurazione
cp config.env.template config.env.dev

# Modifica i valori in config.env.dev con le tue credenziali
```

### Variabili di Ambiente per Portal

| Variabile                 | Descrizione                                | Esempio                                |
| ------------------------- | ------------------------------------------ | -------------------------------------- |
| `VITE_PORTAL_CLIENT_ID`   | Client ID dell'app registration Portal SPA | `60bc8a84-b463-4254-84a9-fe360efb2943` |
| `VITE_TENANT_ID`          | Tenant ID di EntraID                       | `648900eb-28ff-4b9c-b696-1fdbe561a082` |
| `VITE_AUTH_API_CLIENT_ID` | Client ID dell'Auth API                    | `36dc2b3a-72c2-4ae7-82e9-61737f8a83e0` |
| `VITE_AUTH_API_URL`       | URL dell'Auth API                          | `http://localhost:8003`                |
| `VITE_PORTAL_URL`         | URL del Portal                             | `http://localhost:3000`                |

**Nota**: Vite carica automaticamente i file `.env` nella directory del progetto. Per usare il file centralizzato, crea un symlink o copia il file:

```bash
# Opzione 1: Symlink (consigliato)
ln -s ../config.env.dev .env.local

# Opzione 2: Copia il file
cp ../config.env.dev .env.local
```

## Sviluppo

```bash
# Installa dipendenze
npm install

# Avvia in modalità sviluppo
npm run dev

# Build per produzione
npm run build
```

## Architettura Autenticazione

Il Portal gestisce l'autenticazione centralizata per tutti i microservizi:

1. **Login**: Utente si autentica tramite EntraID
2. **Token Storage**: I token vengono salvati in localStorage
3. **Token Sharing**: Altri frontend leggono i token dal localStorage
4. **Token Validation**: Le API business validano i token tramite Auth API

## EntraID App Registrations

### Portal SPA

- **Tipo**: Single-page application
- **Redirect URI**: `http://localhost:3000`
- **Scopes**: `openid`, `profile`, `email`, `User.Read`

### Auth API

- **Tipo**: Web app/API
- **Espone Scope**: `access_as_user`
- **Permessi**: Microsoft Graph User.Read.All
