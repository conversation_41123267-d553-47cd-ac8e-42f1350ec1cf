import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { PublicClientApplication, EventType } from "@azure/msal-browser";
import { Msal<PERSON>rovider } from "@azure/msal-react";
import { msalConfig } from "./auth/msalConfig";
import "./index.css";
import App from "./App.tsx";

// Create MSAL instance
const msalInstance = new PublicClientApplication(msalConfig);

// Initialize MSAL and handle redirect promise
msalInstance
  .initialize()
  .then(() => {
    return msalInstance.handleRedirectPromise();
  })
  .then((response) => {
    // Handle the response if needed
    if (response) {
      console.log("Redirect response:", response);
    }

    // Set up event callback for login success
    msalInstance.addEventCallback((event) => {
      if (event.eventType === EventType.LOGIN_SUCCESS) {
        // Set the active account from the response
        if (response && response.account) {
          msalInstance.setActiveAccount(response.account);
          console.log(
            "Login success, active account set:",
            response.account.username
          );
        }
      }
    });

    // Default to using the first account if no account is active on page load
    if (
      !msalInstance.getActiveAccount() &&
      msalInstance.getAllAccounts().length > 0
    ) {
      msalInstance.setActiveAccount(msalInstance.getAllAccounts()[0]);
    }

    // Render the app after handling redirects
    createRoot(document.getElementById("root")!).render(
      <StrictMode>
        <MsalProvider instance={msalInstance}>
          <App />
        </MsalProvider>
      </StrictMode>
    );
  })
  .catch((error) => {
    console.error("Error handling redirect promise:", error);

    // Still render the app even if redirect handling fails
    createRoot(document.getElementById("root")!).render(
      <StrictMode>
        <MsalProvider instance={msalInstance}>
          <App />
        </MsalProvider>
      </StrictMode>
    );
  });
