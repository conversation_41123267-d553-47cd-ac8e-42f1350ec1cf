import type { Configuration } from "@azure/msal-browser";
import { LogLevel } from "@azure/msal-browser";

// Environment configuration - can be overridden by environment variables
const config = {
  // ⚠️ DEVELOPMENT ONLY: Replace with your production Portal SPA Client ID for deployment
  CLIENT_ID:
    import.meta.env.VITE_PORTAL_CLIENT_ID ||
    "60bc8a84-b463-4254-84a9-fe360efb2943",
  // ⚠️ DEVELOPMENT ONLY: Replace with your production Azure Tenant ID for deployment
  TENANT_ID:
    import.meta.env.VITE_TENANT_ID || "648900eb-28ff-4b9c-b696-1fdbe561a082",
  // ⚠️ DEVELOPMENT ONLY: Replace with your production Auth API Client ID for deployment
  AUTH_API_CLIENT_ID:
    import.meta.env.VITE_AUTH_API_CLIENT_ID ||
    "36dc2b3a-72c2-4ae7-82e9-61737f8a83e0",
  PORTAL_URL: import.meta.env.VITE_PORTAL_URL || "http://localhost:3000",
  AUTH_API_URL: import.meta.env.VITE_AUTH_API_URL || "http://localhost:8003",
};

// MSAL configuration for Portal SPA
export const msalConfig: Configuration = {
  auth: {
    clientId: config.CLIENT_ID,
    authority: `https://login.microsoftonline.com/${config.TENANT_ID}`,
    redirectUri: config.PORTAL_URL,
    postLogoutRedirectUri: config.PORTAL_URL,
  },
  cache: {
    cacheLocation: "localStorage", // Store tokens in localStorage for sharing
    storeAuthStateInCookie: false, // Set to true for IE11 or Edge
  },
  system: {
    loggerOptions: {
      loggerCallback: (level, message, containsPii) => {
        if (containsPii) {
          return;
        }
        switch (level) {
          case LogLevel.Error:
            console.error(message);
            return;
          case LogLevel.Info:
            console.info(message);
            return;
          case LogLevel.Verbose:
            console.debug(message);
            return;
          case LogLevel.Warning:
            console.warn(message);
            return;
        }
      },
    },
  },
};

// Scopes for login request - only basic OpenID scopes
export const loginRequest = {
  scopes: [
    "openid",
    "profile",
    "email",
    "User.Read", // Microsoft Graph scope to read user profile
  ],
};

// Scopes for calling the Auth API (when needed for token validation)
export const authApiRequest = {
  scopes: [`api://${config.AUTH_API_CLIENT_ID}/access_as_user`],
};

// Auth API base URL
export const AUTH_API_BASE_URL = config.AUTH_API_URL;
