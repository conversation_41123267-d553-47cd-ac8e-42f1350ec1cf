import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  type ReactNode,
} from "react";
import { useMsal, useAccount } from "@azure/msal-react";
import type { AccountInfo } from "@azure/msal-browser";
import {
  loginRequest,
  authApiRequest,
  AUTH_API_BASE_URL,
  msalConfig,
} from "./msalConfig";

interface AuthContextType {
  isAuthenticated: boolean;
  user: AccountInfo | null;
  accessToken: string | null;
  login: () => Promise<void>;
  logout: () => Promise<void>;
  getAccessToken: () => Promise<string | null>;
  getAuthApiToken: () => Promise<string | null>;
  callAuthApi: (endpoint: string, options?: RequestInit) => Promise<Response>;
  isLoading: boolean;
  error: string | null;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isLoginInProgress, setIsLoginInProgress] = useState(false);

  const { instance, accounts } = useMsal();
  const account = useAccount(accounts[0] || {});

  const isAuthenticated = !!account;

  useEffect(() => {
    setIsLoading(false);
    if (account) {
      // Get basic access token and store user info
      getAccessToken();
      // Also get Auth API token for business API calls
      getAuthApiToken();
    }
  }, [account]);

  const login = async () => {
    try {
      setError(null);

      // Check if login is already in progress
      if (isLoginInProgress) {
        console.log("Login already in progress, skipping...");
        return;
      }

      // Check if user is already logged in
      if (instance.getAllAccounts().length > 0) {
        console.log("User already logged in");
        return;
      }

      setIsLoginInProgress(true);
      console.log("Starting login redirect...");

      // Use basic login scopes (no Auth API scope)
      await instance.loginRedirect({
        ...loginRequest,
        prompt: "select_account",
      });
    } catch (error) {
      console.error("Login failed:", error);
      // Check if it's the interaction_in_progress error specifically
      const errorMessage = String(error);
      if (errorMessage.includes("interaction_in_progress")) {
        console.log("Interaction already in progress, ignoring error");
        return;
      }
      setError("Errore durante il login");
    } finally {
      // Reset the flag after a delay to allow for redirect
      setTimeout(() => {
        setIsLoginInProgress(false);
      }, 1000);
    }
  };

  const logout = async () => {
    try {
      setError(null);

      console.log("=== LOGOUT DEBUG ===");
      console.log("Current account:", account);
      console.log("MSAL Config:", msalConfig);
      console.log(
        "Post logout redirect URI:",
        msalConfig.auth.postLogoutRedirectUri
      );
      console.log("Authority:", msalConfig.auth.authority);

      // Clear tokens from localStorage first
      localStorage.removeItem("ndue_access_token");
      localStorage.removeItem("ndue_auth_api_token");
      localStorage.removeItem("ndue_user_info");
      setAccessToken(null);

      // Clear all MSAL cache to avoid tenant ID issues
      await instance.clearCache();
      console.log("Cache cleared");

      // Use the configured postLogoutRedirectUri from msalConfig
      const logoutRequest = {
        postLogoutRedirectUri: msalConfig.auth.postLogoutRedirectUri,
        account: account || undefined,
      };

      console.log("Logout request:", logoutRequest);

      await instance.logoutRedirect(logoutRequest);
    } catch (error) {
      console.error("Logout failed:", error);
      setError("Errore durante il logout");
    }
  };

  const getAccessToken = async (): Promise<string | null> => {
    if (!account) return null;

    try {
      // Get basic token with login scopes
      const response = await instance.acquireTokenSilent({
        ...loginRequest,
        account: account,
      });

      const token = response.accessToken;
      setAccessToken(token);

      // Store basic token and user info for other frontends
      localStorage.setItem("ndue_access_token", token);
      localStorage.setItem(
        "ndue_user_info",
        JSON.stringify({
          id: account.localAccountId,
          email: account.username,
          name: account.name,
          token: token,
        })
      );

      return token;
    } catch (error) {
      console.error("Token acquisition failed:", error);
      setError("Errore nell'acquisizione del token");
      return null;
    }
  };

  const getAuthApiToken = async (): Promise<string | null> => {
    if (!account) return null;

    try {
      // Get specific token for Auth API
      const response = await instance.acquireTokenSilent({
        ...authApiRequest,
        account: account,
      });

      const authApiToken = response.accessToken;

      // Store Auth API token separately
      localStorage.setItem("ndue_auth_api_token", authApiToken);

      return authApiToken;
    } catch (error) {
      console.error("Auth API token acquisition failed:", error);
      // Try interactive acquisition
      try {
        const response = await instance.acquireTokenPopup({
          ...authApiRequest,
          account: account,
        });

        const authApiToken = response.accessToken;
        localStorage.setItem("ndue_auth_api_token", authApiToken);
        return authApiToken;
      } catch (popupError) {
        console.error(
          "Interactive Auth API token acquisition failed:",
          popupError
        );
        setError("Impossibile ottenere il token per l'Auth API");
        return null;
      }
    }
  };

  const callAuthApi = async (
    endpoint: string,
    options: RequestInit = {}
  ): Promise<Response> => {
    const token = await getAuthApiToken();
    if (!token) {
      throw new Error("No Auth API token available");
    }

    const url = `${AUTH_API_BASE_URL}${endpoint}`;
    const headers = {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
      ...options.headers,
    };

    return fetch(url, {
      ...options,
      headers,
    });
  };

  const contextValue: AuthContextType = {
    isAuthenticated,
    user: account,
    accessToken,
    login,
    logout,
    getAccessToken,
    getAuthApiToken,
    callAuthApi,
    isLoading,
    error,
  };

  return (
    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
