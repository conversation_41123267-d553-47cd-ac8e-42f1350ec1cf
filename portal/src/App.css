#root {
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

.portal-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.portal-header {
  background-color: #1a1a1a;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  width: 100%;
  min-width: 100vw;
  max-width: 100vw;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  box-sizing: border-box;
}

.portal-header h1 {
  color: white;
  margin: 0;
  font-size: 1.5rem;
}

.portal-header nav {
  display: flex;
  gap: 1rem;
}

.nav-link {
  color: #646cff;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.nav-link:hover {
  background-color: rgba(100, 108, 255, 0.1);
  color: #747bff;
}

.portal-content {
  flex: 1;
  padding: 2rem;
  margin-top: 80px;
  overflow-x: auto;
}

.portal-content h2 {
  color: #1a1a1a;
  margin-bottom: 1rem;
  font-weight: 600;
}

.portal-content p {
  color: #2a2a2a;
  line-height: 1.6;
  font-weight: 500;
}
