import React, { useState, useEffect } from "react";
import { useAuth } from "../../auth/AuthContext";

interface UserInfo {
  id: string;
  email: string;
  name: string;
  displayName?: string;
  roles?: string[];
  claims?: Record<string, unknown>;
}

interface TokenInfo {
  accessToken: string;
  authApiToken: string;
  storedAccessToken: string;
  storedAuthApiToken: string;
  accessTokenLength: number;
  authApiTokenLength: number;
}

export const UserProfile: React.FC = () => {
  const { user, callAuthApi, getAuthApiToken, getAccessToken } = useAuth();
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [tokenInfo, setTokenInfo] = useState<TokenInfo | null>(null);
  const [loading, setLoading] = useState(false);

  const fetchUserInfo = async () => {
    setLoading(true);

    try {
      const response = await callAuthApi("/api/auth/user-info");

      if (response.ok) {
        const userData = await response.json();
        setUserInfo(userData);
      } else {
        console.error(`Failed to fetch user info: ${response.status}`);
      }
    } catch (err) {
      console.error(
        `Error fetching user info: ${
          err instanceof Error ? err.message : "Unknown error"
        }`
      );
    } finally {
      setLoading(false);
    }
  };

  const checkTokens = async () => {
    try {
      // Get both tokens
      const accessToken = await getAccessToken();
      const authApiToken = await getAuthApiToken();

      // Check localStorage
      const storedAccessToken = localStorage.getItem("ndue_access_token");
      const storedAuthApiToken = localStorage.getItem("ndue_auth_api_token");

      setTokenInfo({
        accessToken: accessToken ? "Available" : "Not available",
        authApiToken: authApiToken ? "Available" : "Not available",
        storedAccessToken: storedAccessToken ? "Stored" : "Not stored",
        storedAuthApiToken: storedAuthApiToken ? "Stored" : "Not stored",
        accessTokenLength: accessToken?.length || 0,
        authApiTokenLength: authApiToken?.length || 0,
      });
    } catch (error) {
      console.error("Error checking tokens:", error);
    }
  };

  useEffect(() => {
    if (user) {
      fetchUserInfo();
      checkTokens();
    }
  }, [user]);

  if (!user) {
    return <div>Please log in to view profile information.</div>;
  }

  return (
    <div style={{ padding: "2rem", maxWidth: "800px" }}>
      <h2>User Profile</h2>

      <div
        style={{
          backgroundColor: "#f8f9fa",
          padding: "1rem",
          marginBottom: "1rem",
          borderRadius: "4px",
        }}
      >
        <h3>Basic Information</h3>
        <p>
          <strong>Name:</strong> {user.name || "N/A"}
        </p>
        <p>
          <strong>Email:</strong> {user.username || "N/A"}
        </p>
        <p>
          <strong>Account ID:</strong> {user.localAccountId || "N/A"}
        </p>
      </div>

      {/* Token Status Section */}
      <div
        style={{
          backgroundColor: "#e7f1ff",
          padding: "1rem",
          marginBottom: "1rem",
          borderRadius: "4px",
          border: "1px solid #b3d9ff",
        }}
      >
        <h3>🔐 Token Status</h3>
        <button
          onClick={checkTokens}
          style={{
            backgroundColor: "#007bff",
            color: "white",
            border: "none",
            padding: "0.5rem 1rem",
            borderRadius: "4px",
            cursor: "pointer",
            marginBottom: "1rem",
          }}
        >
          Refresh Token Status
        </button>

        {tokenInfo && (
          <div>
            <p>
              <strong>Access Token:</strong> {tokenInfo.accessToken} (
              {tokenInfo.accessTokenLength} chars)
            </p>
            <p>
              <strong>Auth API Token:</strong> {tokenInfo.authApiToken} (
              {tokenInfo.authApiTokenLength} chars)
            </p>
            <p>
              <strong>Stored Access Token:</strong>{" "}
              {tokenInfo.storedAccessToken}
            </p>
            <p>
              <strong>Stored Auth API Token:</strong>{" "}
              {tokenInfo.storedAuthApiToken}
            </p>

            {tokenInfo.authApiToken === "Available" ? (
              <div style={{ color: "green", fontWeight: "bold" }}>
                ✅ Auth API token is available - federated frontends should
                work!
              </div>
            ) : (
              <div style={{ color: "red", fontWeight: "bold" }}>
                ❌ Auth API token is missing - federated frontends will get 401
                errors
              </div>
            )}
          </div>
        )}
      </div>

      <div
        style={{
          backgroundColor: "#f8f9fa",
          padding: "1rem",
          marginBottom: "1rem",
          borderRadius: "4px",
        }}
      >
        <h3>Auth API Test</h3>
        <button
          onClick={fetchUserInfo}
          disabled={loading}
          style={{
            backgroundColor: "#28a745",
            color: "white",
            border: "none",
            padding: "0.5rem 1rem",
            borderRadius: "4px",
            cursor: "pointer",
            marginBottom: "1rem",
          }}
        >
          {loading ? "Loading..." : "Test Auth API Connection"}
        </button>

        {userInfo && (
          <div>
            <h4>Auth API Response:</h4>
            <pre
              style={{
                backgroundColor: "#e9ecef",
                padding: "1rem",
                borderRadius: "4px",
                overflow: "auto",
                fontSize: "0.875rem",
              }}
            >
              {JSON.stringify(userInfo, null, 2)}
            </pre>
          </div>
        )}
      </div>

      {/* Instructions Section */}
      <div
        style={{
          backgroundColor: "#fff3cd",
          padding: "1rem",
          borderRadius: "4px",
          border: "1px solid #ffeaa7",
        }}
      >
        <h3>📋 Testing Instructions</h3>
        <ol>
          <li>Check that "Auth API Token" shows as "Available" above</li>
          <li>Test the Auth API connection with the button</li>
          <li>Navigate to Gruppi or Eventi pages to test federated apps</li>
          <li>Check browser console for authentication logs</li>
        </ol>
      </div>
    </div>
  );
};
