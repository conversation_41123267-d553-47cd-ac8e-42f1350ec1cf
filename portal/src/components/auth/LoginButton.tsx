import React from "react";
import { useAuth } from "../../auth/AuthContext";

// Simple fallback button for debugging
const SimpleLoginButton = () => (
  <div
    style={{
      padding: "0.5rem",
      backgroundColor: "rgba(255, 255, 255, 0.1)",
      borderRadius: "4px",
      color: "white",
    }}
  >
    ⚠️ Errore nell'autenticazione
    <button
      onClick={() => {
        console.log("Simple login button clicked - MSAL not available");
        window.location.reload();
      }}
      style={{
        marginLeft: "0.5rem",
        padding: "0.25rem 0.5rem",
        backgroundColor: "#007bff",
        color: "white",
        border: "none",
        borderRadius: "4px",
        cursor: "pointer",
        fontSize: "12px",
      }}
    >
      Ricarica
    </button>
  </div>
);

export const LoginButton: React.FC = () => {
  const [isActionInProgress, setIsActionInProgress] = React.useState(false);
  console.log("LoginButton component rendering...");

  try {
    const authResult = useAuth();
    console.log("useAuth called successfully");

    const { isAuthenticated, user, login, logout, isLoading, error } =
      authResult;
    console.log("Auth state:", {
      isAuthenticated,
      user: user?.name,
      isLoading,
      error,
      isActionInProgress,
    });

    const handleAction = async () => {
      if (isActionInProgress) {
        console.log("Action already in progress, ignoring click");
        return;
      }

      try {
        setIsActionInProgress(true);
        console.log("Login button clicked, isAuthenticated:", isAuthenticated);
        if (isAuthenticated) {
          await logout();
        } else {
          await login();
        }
      } catch (error) {
        console.error("Authentication action failed:", error);
        alert("Errore durante l'autenticazione: " + error);
      } finally {
        setIsActionInProgress(false);
      }
    };

    if (isLoading || isActionInProgress) {
      console.log("Showing loading state");
      return (
        <div
          style={{
            color: "white",
            fontSize: "14px",
          }}
        >
          🔄{" "}
          {isActionInProgress ? "Autenticazione in corso..." : "Caricamento..."}
        </div>
      );
    }

    if (error) {
      console.log("Showing error state:", error);
      return (
        <div
          style={{
            color: "#ffcccc",
            fontSize: "14px",
          }}
        >
          ⚠️ Errore
          <button
            onClick={() => window.location.reload()}
            style={{
              marginLeft: "0.5rem",
              padding: "0.25rem 0.5rem",
              backgroundColor: "#007bff",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
              fontSize: "12px",
            }}
          >
            Ricarica
          </button>
        </div>
      );
    }

    console.log("Rendering normal login button state");
    return (
      <div
        style={{
          color: "white",
          display: "flex",
          alignItems: "center",
          gap: "0.75rem",
        }}
      >
        {isAuthenticated ? (
          <>
            <span style={{ fontSize: "14px", fontWeight: "500" }}>
              Ciao {user?.name || user?.username || "Utente"}! 👋
            </span>
            <button
              onClick={handleAction}
              style={{
                padding: "0.5rem 1rem",
                backgroundColor: "#dc3545",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: "pointer",
                fontSize: "14px",
                fontWeight: "500",
                transition: "background-color 0.2s",
              }}
              onMouseOver={(e) =>
                ((e.target as HTMLButtonElement).style.backgroundColor =
                  "#c82333")
              }
              onMouseOut={(e) =>
                ((e.target as HTMLButtonElement).style.backgroundColor =
                  "#dc3545")
              }
            >
              Logout
            </button>
          </>
        ) : (
          <button
            onClick={handleAction}
            style={{
              padding: "0.75rem 1.5rem",
              backgroundColor: "#007bff",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
              fontSize: "14px",
              fontWeight: "500",
              transition: "background-color 0.2s",
            }}
            onMouseOver={(e) =>
              ((e.target as HTMLButtonElement).style.backgroundColor =
                "#0056b3")
            }
            onMouseOut={(e) =>
              ((e.target as HTMLButtonElement).style.backgroundColor =
                "#007bff")
            }
          >
            🔐 Accedi con Microsoft
          </button>
        )}
      </div>
    );
  } catch (error) {
    console.error("LoginButton component error:", error);
    console.log("Falling back to simple button");
    return <SimpleLoginButton />;
  }
};
