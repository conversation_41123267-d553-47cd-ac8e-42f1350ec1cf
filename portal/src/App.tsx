import { Suspense, lazy } from "react";
import {
  create<PERSON><PERSON>er<PERSON><PERSON>er,
  RouterProvider,
  Outlet,
  Link,
} from "react-router-dom";
import {
  AuthenticatedTemplate,
  UnauthenticatedTemplate,
} from "@azure/msal-react";
import { AuthProvider } from "./auth/AuthContext";
import { LoginButton } from "./components/auth/LoginButton";
import { UserProfile } from "./components/auth/UserProfile";
import "./App.css";

const GruppiApp = lazy(() =>
  import("gruppiFrontendService/App").catch(() => ({
    default: () => (
      <div style={{ padding: "2rem", textAlign: "center" }}>
        <h3>⚠️ Gruppi Frontend Service Unavailable</h3>
        <p>Make sure the Gruppi Frontend service is running on port 3002</p>
        <p>
          Check:{" "}
          <a href="http://localhost:3002" target="_blank">
            http://localhost:3002
          </a>
        </p>
      </div>
    ),
  }))
);

const EventiTeleriscaldamentoApp = lazy(() =>
  import("eventiTeleriscaldamentoFe/App").catch(() => ({
    default: () => (
      <div style={{ padding: "2rem", textAlign: "center" }}>
        <h3>⚠️ Eventi Teleriscaldamento Frontend Unavailable</h3>
        <p>
          Make sure the Eventi Teleriscaldamento Frontend service is running on
          port 3005
        </p>
        <p>
          Check:{" "}
          <a href="http://localhost:3005" target="_blank">
            http://localhost:3005
          </a>
        </p>
      </div>
    ),
  }))
);

function RootLayout() {
  return (
    <AuthProvider>
      <div className="portal-layout">
        <header
          className="portal-header"
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "1rem 2rem",
            backgroundColor: "#343a40",
            color: "white",
            boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
          }}
        >
          <div style={{ display: "flex", alignItems: "center", gap: "2rem" }}>
            <h1 style={{ margin: 0, fontSize: "1.5rem" }}>
              NDUE Next Generation
            </h1>
            <nav style={{ display: "flex", gap: "1rem" }}>
              <Link
                to="/"
                className="nav-link"
                style={{
                  color: "white",
                  textDecoration: "none",
                  padding: "0.5rem 1rem",
                  borderRadius: "4px",
                  transition: "background-color 0.2s",
                }}
              >
                Home
              </Link>
              <AuthenticatedTemplate>
                <Link
                  to="/profile"
                  className="nav-link"
                  style={{
                    color: "white",
                    textDecoration: "none",
                    padding: "0.5rem 1rem",
                    borderRadius: "4px",
                    transition: "background-color 0.2s",
                  }}
                >
                  Profile
                </Link>
                <Link
                  to="/gruppi"
                  className="nav-link"
                  style={{
                    color: "white",
                    textDecoration: "none",
                    padding: "0.5rem 1rem",
                    borderRadius: "4px",
                    transition: "background-color 0.2s",
                  }}
                >
                  Gruppi
                </Link>
                <Link
                  to="/eventi-teleriscaldamento"
                  className="nav-link"
                  style={{
                    color: "white",
                    textDecoration: "none",
                    padding: "0.5rem 1rem",
                    borderRadius: "4px",
                    transition: "background-color 0.2s",
                  }}
                >
                  Eventi Teleriscaldamento
                </Link>
              </AuthenticatedTemplate>
            </nav>
          </div>

          {/* Login button in top right */}
          <div>
            <Suspense
              fallback={<div style={{ color: "white" }}>Caricamento...</div>}
            >
              <LoginButton />
            </Suspense>
          </div>
        </header>

        <main className="portal-content" style={{ padding: "2rem" }}>
          <Suspense fallback={<div>Loading...</div>}>
            <Outlet />
          </Suspense>
        </main>
      </div>
    </AuthProvider>
  );
}

function HomePage() {
  return (
    <div>
      <h2>Welcome to NDUE Next Generation</h2>
      <AuthenticatedTemplate>
        <p>
          You are successfully authenticated! Navigate to different
          microservices using the menu above.
        </p>
        <div
          style={{
            backgroundColor: "#d4edda",
            padding: "1rem",
            borderRadius: "4px",
            margin: "1rem 0",
          }}
        >
          <h3>🔐 Authentication Status: Active</h3>
          <p>
            ✅ Your authentication tokens are being shared with all
            microservices
          </p>
          <p>✅ Auth API is ready to validate your tokens</p>
          <p>
            📝 Check your <Link to="/profile">Profile</Link> to see detailed
            authentication information
          </p>
        </div>
      </AuthenticatedTemplate>

      <UnauthenticatedTemplate>
        <p>Please sign in to access the NDUE microservices.</p>
        <div
          style={{
            backgroundColor: "#fff3cd",
            padding: "1rem",
            borderRadius: "4px",
            margin: "1rem 0",
          }}
        >
          <h3>🔐 Authentication Required</h3>
          <p>This portal handles authentication for all NDUE microservices.</p>
          <p>Sign in with your Microsoft account to get started.</p>
        </div>
      </UnauthenticatedTemplate>
    </div>
  );
}

const router = createBrowserRouter([
  {
    path: "/",
    element: <RootLayout />,
    children: [
      {
        index: true,
        element: <HomePage />,
      },
      {
        path: "profile",
        element: <UserProfile />,
      },
      {
        path: "gruppi/*",
        element: (
          <AuthenticatedTemplate>
            <GruppiApp />
          </AuthenticatedTemplate>
        ),
      },
      {
        path: "eventi-teleriscaldamento/*",
        element: (
          <AuthenticatedTemplate>
            <EventiTeleriscaldamentoApp />
          </AuthenticatedTemplate>
        ),
      },
    ],
  },
]);

function App() {
  return <RouterProvider router={router} />;
}

export default App;
