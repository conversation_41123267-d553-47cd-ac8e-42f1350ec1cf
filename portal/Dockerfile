# Dockerfile for Portal Frontend (Authentication Hub)
# NDUE Next Generation Microservices Platform

#############################
# Stage 1: Build Environment
#############################
FROM node:24.3.0-alpine AS build

# Set working directory
WORKDIR /app

# Install build dependencies
RUN apk add --no-cache python3 make g++ git

# Copy package files first for better Docker layer caching
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production --silent

# Copy source code
COPY . .

# Build arguments for Portal-specific environment variables
ARG VITE_PORTAL_CLIENT_ID
ARG VITE_TENANT_ID  
ARG VITE_AUTH_API_CLIENT_ID
ARG VITE_PORTAL_URL
ARG VITE_AUTH_API_URL
ARG VITE_DOMAIN
ARG NODE_ENV=production

# Set environment variables for build
ENV VITE_PORTAL_CLIENT_ID=${VITE_PORTAL_CLIENT_ID}
ENV VITE_TENANT_ID=${VITE_TENANT_ID}
ENV VITE_AUTH_API_CLIENT_ID=${VITE_AUTH_API_CLIENT_ID}
ENV VITE_PORTAL_URL=${VITE_PORTAL_URL}
ENV VITE_AUTH_API_URL=${VITE_AUTH_API_URL}
ENV VITE_DOMAIN=${VITE_DOMAIN}
ENV NODE_ENV=${NODE_ENV}

# Build the Portal application
RUN npm run build

# Verify Portal-specific build artifacts
RUN ls -la dist/ && \
    echo "Portal build completed successfully" && \
    du -sh dist/

#############################
# Stage 2: Production Server
#############################
FROM nginx:1.25.3-alpine AS production

# Install curl for health checks
RUN apk add --no-cache curl

# Remove default nginx content
RUN rm -rf /usr/share/nginx/html/*

# Copy built Portal application
COPY --from=build /app/dist /usr/share/nginx/html

# Copy Portal-specific nginx configuration
COPY portal-nginx.conf /etc/nginx/nginx.conf

# Set proper permissions
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chmod -R 755 /usr/share/nginx/html && \
    chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d && \
    mkdir -p /var/run/nginx && \
    chown nginx:nginx /var/run/nginx

# Switch to non-root user
USER nginx

# Expose port 8080
EXPOSE 8080

# Health check specific to Portal
HEALTHCHECK --interval=30s --timeout=3s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:8080/ || exit 1

# Labels for better container management
LABEL org.opencontainers.image.title="NDUE Portal Frontend"
LABEL org.opencontainers.image.description="Portal frontend with EntraID authentication"
LABEL org.opencontainers.image.vendor="NDUE"
LABEL org.opencontainers.image.version="1.0.0"

# Start nginx
CMD ["nginx", "-g", "daemon off;"] 