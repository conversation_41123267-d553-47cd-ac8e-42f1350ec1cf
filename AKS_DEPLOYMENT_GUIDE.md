# AKS Deployment Guide - FQDN Strategy

## 🎯 FQDN Recommendation: **Single Domain with Path-Based Routing**

For your NDUE microservices architecture, you need **ONE FQDN** with path-based routing because:

### ✅ Why Single FQDN Works Best

1. **Token Sharing**: Your Portal shares authentication tokens via `localStorage` with micro-frontends - this **requires same origin**
2. **Module Federation**: Portal loads remote components from micro-frontends at runtime
3. **EntraID Configuration**: Single redirect URI simplifies Azure AD setup
4. **CORS Simplification**: Same-origin requests avoid complex CORS configuration

## 🌐 Recommended URL Structure

```
https://ndue.your-domain.com/              # Portal (main app)
https://ndue.your-domain.com/api/auth/     # Auth API
https://ndue.your-domain.com/api/gruppi/   # Gruppi API
https://ndue.your-domain.com/api/eventi/   # Eventi API
https://ndue.your-domain.com/gruppi/       # Gruppi Frontend (standalone)
https://ndue.your-domain.com/eventi/       # Eventi Frontend (standalone)
```

## 🔧 Required Configuration Changes

### 1. Update Portal Module Federation

You need to update the Portal's Vite configuration for production URLs:

**File**: `portal/vite.config.ts`

```typescript
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { federation } from "@module-federation/vite";

export default defineConfig({
  plugins: [
    react(),
    federation({
      name: "portal",
      remotes: {
        gruppiFrontendService: {
          type: "module",
          name: "gruppiFrontendService",
          // Production URL instead of localhost
          entry: `${
            process.env.VITE_DOMAIN || "http://localhost:3002"
          }/remoteEntry.js`,
          entryGlobalName: "gruppiFrontendService",
          shareScope: "default",
        },
        eventiTeleriscaldamentoFe: {
          type: "module",
          name: "eventiTeleriscaldamentoFe",
          // Production URL instead of localhost
          entry: `${
            process.env.VITE_DOMAIN || "http://localhost:3005"
          }/remoteEntry.js`,
          entryGlobalName: "eventiTeleriscaldamentoFe",
          shareScope: "default",
        },
      },
      // ... rest of config
    }),
  ],
});
```

### 2. Update Azure AD App Registrations

**Create NEW production app registrations** (don't modify development ones):

#### Portal SPA Registration

- **Redirect URI**: `https://ndue.your-domain.com`
- **Logout URL**: `https://ndue.your-domain.com`
- **Type**: Single-page application

#### Auth API Registration

- **Application ID URI**: `api://your-production-auth-api-client-id`
- **Type**: Web app/API

### 3. Environment Configuration

Use the provided `config.env.prod` file and update these critical values:

```bash
# Replace with your actual production values
DOMAIN=ndue.your-domain.com
VITE_PORTAL_CLIENT_ID=your-production-portal-spa-client-id
VITE_TENANT_ID=your-production-tenant-id
AZURE_CLIENT_ID=your-production-auth-api-client-id
PGHOST=your-production-postgres-host
```

## 🚀 AKS Deployment Steps

### 1. Deploy Infrastructure

```bash
# Apply the ingress configuration
kubectl apply -f infrastructure/nginx-ingress.yaml

# Verify ingress is created
kubectl get ingress -n ndue
```

### 2. SSL Certificate Setup

**Option A: Let's Encrypt (Automated)**

```bash
# Install cert-manager if not already installed
kubectl apply -f https://github.com/jetstack/cert-manager/releases/download/v1.12.0/cert-manager.yaml

# Certificate will be automatically provisioned via ingress annotations
```

**Option B: Azure Key Vault (Recommended for production)**

```bash
# Create certificate in Azure Key Vault
az keyvault certificate create \
  --vault-name your-keyvault \
  --name ndue-ssl-cert \
  --policy "$(az keyvault certificate get-default-policy)"
```

### 3. Deploy Services

Create and apply Kubernetes deployments for each service:

```bash
# Deploy all services
kubectl apply -f infrastructure/portal-deployment.yaml
kubectl apply -f infrastructure/auth-api-deployment.yaml
kubectl apply -f infrastructure/gruppi-api-deployment.yaml
kubectl apply -f infrastructure/eventi-api-deployment.yaml
kubectl apply -f infrastructure/gruppi-frontend-deployment.yaml
kubectl apply -f infrastructure/eventi-frontend-deployment.yaml
```

### 4. Configure DNS

Point your domain to the AKS ingress controller:

```bash
# Get the external IP of your ingress controller
kubectl get services -n ingress-nginx

# Create DNS A record
ndue.your-domain.com → [INGRESS_EXTERNAL_IP]
```

## 🔒 Security Considerations

### Environment Variables in AKS

**Option 1: Kubernetes Secrets**

```bash
kubectl create secret generic ndue-config \
  --from-literal=AZURE_CLIENT_ID=your-client-id \
  --from-literal=PGPASSWORD=your-db-password \
  --namespace=ndue
```

**Option 2: Azure Key Vault (Recommended)**

```bash
# Use Azure Key Vault Provider for Secrets Store CSI Driver
# Reference secrets directly from Key Vault
```

### Update CORS Configuration

All your APIs need the same origin for CORS:

```json
{
  "Cors": {
    "AllowedOrigins": ["https://ndue.your-domain.com"]
  }
}
```

## 🧪 Testing Deployment

### 1. Verify URL Structure

```bash
# Test each endpoint
curl -I https://ndue.your-domain.com/
curl -I https://ndue.your-domain.com/api/auth/health
curl -I https://ndue.your-domain.com/api/gruppi
curl -I https://ndue.your-domain.com/gruppi/
```

### 2. Test Authentication Flow

1. Navigate to `https://ndue.your-domain.com`
2. Sign in with your Microsoft account
3. Verify tokens are stored in localStorage
4. Navigate to `/gruppi` and `/eventi` - should load without auth issues

### 3. Test Module Federation

1. Check browser Network tab for successful remote entry loads:
   - `https://ndue.your-domain.com/gruppi/remoteEntry.js`
   - `https://ndue.your-domain.com/eventi/remoteEntry.js`

## ❌ Alternative Options (Not Recommended)

### Why NOT Multiple FQDNs?

**Option: Subdomain per service**

```
https://portal.ndue.your-domain.com
https://gruppi.ndue.your-domain.com
https://eventi.ndue.your-domain.com
```

**Problems:**

- ❌ **localStorage token sharing breaks** (different origins)
- ❌ **CORS complexity** for cross-origin requests
- ❌ **Multiple SSL certificates** needed
- ❌ **Multiple EntraID redirect URIs** to manage

**Option: Apps + APIs split**

```
https://ndue.your-domain.com         # All frontends
https://api.ndue.your-domain.com     # All APIs
```

**Issues:**

- ⚠️ **CORS configuration** required for cross-origin API calls
- ⚠️ **Two SSL certificates** needed
- ⚠️ **More complex ingress** routing

## 📊 Summary

| Approach                | FQDNs Needed | Token Sharing | SSL Certs | CORS Complexity | Recommendation     |
| ----------------------- | ------------ | ------------- | --------- | --------------- | ------------------ |
| **Single FQDN + Paths** | **1**        | ✅ Works      | 1         | None            | ⭐ **RECOMMENDED** |
| Apps + APIs Split       | 2            | ✅ Works      | 2         | Medium          | 🟡 Acceptable      |
| Subdomain per Service   | 6            | ❌ Broken     | 6         | High            | ❌ Not Recommended |

## 🎉 Final Answer

**You need ONE FQDN** for optimal deployment of your microservices architecture. The single domain with path-based routing approach best supports your authentication token sharing mechanism while keeping complexity minimal.

Configure your AKS deployment with:

- **1 FQDN**: `https://ndue.your-domain.com`
- **Path-based routing** for all services
- **1 SSL certificate**
- **1 EntraID redirect URI**
- **No CORS complexity**
