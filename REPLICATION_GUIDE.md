# Project Replication Guide

This guide provides step-by-step instructions for replicating this microservices platform structure in new projects.

## 🚀 Quick Start - New Project Setup

### 1. Initialize New Project

```bash
# Create new project directory
mkdir my-new-microservices-project
cd my-new-microservices-project

# Initialize git repository
git init
```

### 2. Copy Core Structure

Copy the following files and directories from this project:

```bash
# Core configuration files
cp PROJECT_STRUCTURE_TEMPLATE.md ./
cp config.env.template ./
cp start-local.sh ./
cp Dockerfile.frontend ./
cp nginx.conf ./

# Template directory
cp -r templates/ ./

# Shared components (customize as needed)
cp -r shared/ ./

# Infrastructure configurations
cp -r infrastructure/ ./

# Documentation files
cp README.md ./README.md.template
cp AUTHENTICATION_SETUP.md ./
cp ENVIRONMENT_SETUP.md ./
cp DEPLOYMENT_CONFIG_GUIDE.md ./
```

### 3. Create Portal (Shell Application)

```bash
# Create portal directory
mkdir portal
cd portal

# Initialize React + TypeScript project
npm create vite@latest . -- --template react-ts

# Install Module Federation
npm install @module-federation/vite

# Install authentication dependencies
npm install @azure/msal-browser @azure/msal-react

# Install routing
npm install react-router-dom
npm install -D @types/react-router-dom

cd ..
```

### 4. Create Authentication API

```bash
# Create auth API
mkdir auth-api
cd auth-api

# Create .NET minimal API
dotnet new webapi -n AuthApi --framework net9.0
cd AuthApi

# Add required packages
dotnet add package Microsoft.AspNetCore.Authentication.JwtBearer
dotnet add package Microsoft.Identity.Web

cd ../..
```

### 5. Make Scripts Executable

```bash
chmod +x start-local.sh
chmod +x templates/create-new-service.sh
```

## 🔧 Creating Your First Microservice

### Using the Automated Script

```bash
# Create a new service (e.g., "products" service)
./templates/create-new-service.sh products 5110 3006
```

This will create:
- `products-api/` - .NET API with Clean Architecture
- `products-fe/` - React micro-frontend with Module Federation
- Updated configuration templates

### Manual Service Creation

If you prefer manual setup:

#### 1. Create API Structure

```bash
mkdir products-api
cd products-api

# Create solution and projects
dotnet new sln -n ProductsService
dotnet new webapi -n ProductsService.Api --framework net9.0
dotnet new classlib -n ProductsService.Application --framework net9.0
dotnet new classlib -n ProductsService.Domain --framework net9.0
dotnet new classlib -n ProductsService.Infrastructure --framework net9.0

# Add to solution
dotnet sln add ProductsService.Api
dotnet sln add ProductsService.Application
dotnet sln add ProductsService.Domain
dotnet sln add ProductsService.Infrastructure

# Add project references
cd ProductsService.Api
dotnet add reference ../ProductsService.Application
dotnet add reference ../ProductsService.Infrastructure

cd ../ProductsService.Application
dotnet add reference ../ProductsService.Domain

cd ../ProductsService.Infrastructure
dotnet add reference ../ProductsService.Domain
dotnet add reference ../ProductsService.Application

cd ../..
```

#### 2. Create Frontend Structure

```bash
mkdir products-fe
cd products-fe

# Initialize React project
npm create vite@latest . -- --template react-ts

# Install Module Federation
npm install @module-federation/vite

cd ..
```

## 📝 Configuration Customization

### 1. Update Environment Configuration

Edit `config.env.template`:

```bash
# Add your service ports
PRODUCTS_API_PORT=5110
PRODUCTS_FRONTEND_PORT=3006
PRODUCTS_API_URL=http://localhost:${PRODUCTS_API_PORT}
PRODUCTS_FRONTEND_URL=http://localhost:${PRODUCTS_FRONTEND_PORT}
```

### 2. Configure Module Federation

Update `portal/vite.config.ts`:

```typescript
remotes: {
  productsService: {
    type: "module",
    name: "productsService",
    entry: "http://localhost:3006/remoteEntry.js",
    entryGlobalName: "productsService",
    shareScope: "default",
  },
  // ... other remotes
}
```

### 3. Add Portal Routing

Update `portal/src/App.tsx`:

```typescript
import { Routes, Route } from 'react-router-dom';

// Lazy load micro-frontends
const ProductsApp = React.lazy(() => import('productsService/App'));

function App() {
  return (
    <Routes>
      <Route path="/products/*" element={
        <Suspense fallback={<div>Loading Products...</div>}>
          <ProductsApp />
        </Suspense>
      } />
      {/* Other routes */}
    </Routes>
  );
}
```

### 4. Update Startup Script

Edit `start-local.sh` to include your new services:

```bash
# Add port cleanup
kill_port ${PRODUCTS_API_PORT:-5110}
kill_port ${PRODUCTS_FRONTEND_PORT:-3006}

# Add service startup
echo "🚀 Starting Products API..."
cd "$BASE_DIR/products-api"
dotnet run --project ProductsService.Api --environment Local >/dev/null 2>&1 &
PRODUCTS_API_PID=$!

echo "🚀 Starting Products Frontend..."
cd "$BASE_DIR/products-fe"
npm run dev >/dev/null 2>&1 &
PRODUCTS_FRONTEND_PID=$!

# Add to monitoring
monitor_service "Products API" "http://localhost:${PRODUCTS_API_PORT}/health"
monitor_service "Products Frontend" "http://localhost:${PRODUCTS_FRONTEND_PORT}"

# Add to cleanup trap
trap 'echo ""; echo "🚑 Stopping all services..."; kill $AUTH_API_PID $PRODUCTS_API_PID $PRODUCTS_FRONTEND_PID 2>/dev/null; echo "✅ All services stopped"; exit' INT
```

## 🔐 Authentication Setup

### 1. Azure AD Configuration

1. Create two App Registrations in Azure AD:
   - **Portal SPA**: For frontend authentication
   - **Auth API**: For token validation

2. Configure redirect URIs and API permissions

3. Update environment variables:

```bash
VITE_PORTAL_CLIENT_ID=your-portal-spa-client-id
VITE_TENANT_ID=your-tenant-id
AZURE_CLIENT_ID=your-auth-api-client-id
AZURE_TENANT_ID=your-tenant-id
```

### 2. Database Configuration

Update PostgreSQL connection:

```bash
PGHOST=your-postgres-host
PGUSER=your-username
PGPASSWORD=your-password
PGDATABASE=your-database
PGPORT=5432
```

## 📦 Shared Components Integration

### 1. Install Shared Components

```bash
cd your-service-fe
npm install ../shared/ui-components
```

### 2. Use Shared Components

```typescript
import { DataTable, LoadingSpinner, useApiData } from '../shared/ui-components';

function MyComponent() {
  const { data, loading, error } = useApiData('/api/my-data');
  
  if (loading) return <LoadingSpinner />;
  if (error) return <ErrorMessage message={error} />;
  
  return <DataTable data={data} theme="blue" />;
}
```

## 🐳 Docker Configuration

### 1. Frontend Dockerfile

Each frontend service can use the generic `Dockerfile.frontend`:

```dockerfile
# Copy the generic Dockerfile
cp Dockerfile.frontend products-fe/Dockerfile
```

### 2. API Dockerfile

Create API-specific Dockerfile:

```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 8080

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src
COPY ["ProductsService.Api/*.csproj", "ProductsService.Api/"]
COPY ["ProductsService.Application/*.csproj", "ProductsService.Application/"]
COPY ["ProductsService.Domain/*.csproj", "ProductsService.Domain/"]
COPY ["ProductsService.Infrastructure/*.csproj", "ProductsService.Infrastructure/"]
RUN dotnet restore "ProductsService.Api/ProductsService.Api.csproj"

COPY . .
WORKDIR "/src/ProductsService.Api"
RUN dotnet build "ProductsService.Api.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "ProductsService.Api.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "ProductsService.Api.dll"]
```

## 🧪 Testing Your Setup

### 1. Install Dependencies

```bash
# Install all frontend dependencies
cd portal && npm install && cd ..
cd products-fe && npm install && cd ..
```

### 2. Start Services

```bash
# Create local environment file
cp config.env.template config.env.local
# Edit config.env.local with your actual values

# Start all services
./start-local.sh
```

### 3. Verify Services

- Portal: http://localhost:3000
- Products Frontend: http://localhost:3006
- Products API: http://localhost:5110/swagger

## 📋 Checklist for New Projects

- [ ] Copy core structure and templates
- [ ] Create portal application
- [ ] Set up authentication API
- [ ] Configure Azure AD app registrations
- [ ] Create first microservice using script
- [ ] Update portal Module Federation config
- [ ] Add routing in portal
- [ ] Update startup script
- [ ] Configure environment variables
- [ ] Test local development setup
- [ ] Set up shared components
- [ ] Create Docker configurations
- [ ] Document service-specific requirements

## 🎯 Best Practices

1. **Consistent naming**: Use kebab-case for directories, PascalCase for .NET
2. **Port management**: Use consistent port ranges for different service types
3. **Environment separation**: Create separate config files for each environment
4. **Documentation**: Update README.md with project-specific information
5. **Version control**: Add appropriate .gitignore files
6. **Health checks**: Implement health endpoints for all services
7. **Logging**: Configure structured logging across all services
8. **Error handling**: Implement consistent error handling patterns

This guide provides everything needed to replicate the microservices platform structure for new projects while maintaining consistency and best practices.
